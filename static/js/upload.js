document.getElementById('uploadForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const fileInput = document.getElementById('audioFile');
    const enableSpeaker = document.getElementById('enableSpeaker').checked;
    const resultDiv = document.getElementById('uploadResult');

    if (!fileInput.files[0]) {
        resultDiv.innerHTML = '<div class="error">请选择音频文件</div>';
        return;
    }

    const file = fileInput.files[0];
    const maxSize = 100 * 1024 * 1024; // 100MB

    if (file.size > maxSize) {
        resultDiv.innerHTML = '<div class="error">文件大小超过100MB限制</div>';
        return;
    }

    const formData = new FormData();
    formData.append('audio', file);
    formData.append('enable_speaker', enableSpeaker);

    resultDiv.innerHTML = '<div class="loading">🔄 正在上传和转录，请稍候...</div>';

    const startTime = Date.now();

    fetch('/transcribe', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        const endTime = Date.now();
        const processingTime = ((endTime - startTime) / 1000).toFixed(2);

        if (data.code === 200) {
            let html = `<div class="success">✅ 转录成功 (耗时: ${processingTime}秒)</div>`;

            if (data.data.segmented_text && data.data.segmented_text.length > 0) {
                html += '<div class="speaker-result">';
                html += '<h4>🎭 说话人分离结果:</h4>';

                data.data.segmented_text.forEach(segment => {
                    const speakerClass = segment.speaker.replace('_', '-').toLowerCase();
                    html += `<div class="segment ${speakerClass}">
                        <span class="speaker-tag">${segment.speaker}</span>
                        <span class="time-tag">(${segment.start_time.toFixed(1)}s-${segment.end_time.toFixed(1)}s)</span>:
                        ${segment.text}
                    </div>`;
                });
                html += '</div>';

                html += `<div class="info">
                    <strong>📝 完整文本:</strong><br>
                    ${data.data.voice_text}
                </div>`;
            } else {
                html += `<div class="info">
                    <strong>📝 转录结果:</strong><br>
                    ${data.data.voice_text}
                </div>`;
            }

            resultDiv.innerHTML = html;
        } else {
            resultDiv.innerHTML = `<div class="error">❌ 转录失败 (${data.code}): ${data.message}</div>`;
        }
    })
    .catch(error => {
        resultDiv.innerHTML = `<div class="error">❌ 网络错误: ${error.message}</div>`;
    });
});

// 文件选择时显示文件信息
document.getElementById('audioFile').addEventListener('change', function(e) {
    const file = e.target.files[0];
    const resultDiv = document.getElementById('uploadResult');

    if (file) {
        const fileSize = (file.size / 1024 / 1024).toFixed(2);
        const fileType = file.type || '未知';

        resultDiv.innerHTML = `<div class="info">
            📁 已选择文件: ${file.name}<br>
            📏 文件大小: ${fileSize} MB<br>
            🎵 文件类型: ${fileType}
        </div>`;
    }
});