// 离线WebSocket转录相关变量
let offlineSocket = null;
let isOfflineProcessing = false;

function startOfflineWebSocket() {
    console.log('🚀 开始离线WebSocket转录');

    // 检查Socket.IO是否加载
    if (typeof io === 'undefined') {
        console.error('❌ Socket.IO未加载');
        document.getElementById('offlineWebSocketResult').innerHTML = '<div class="error">❌ Socket.IO库未加载，请刷新页面重试</div>';
        return;
    }

    const audioFile = document.getElementById('offlineAudioFile').files[0];
    const enableSpeaker = document.getElementById('enableOfflineSpeaker').checked;

    console.log('📁 选择的文件:', audioFile);
    console.log('🎭 启用说话人分离:', enableSpeaker);

    if (!audioFile) {
        document.getElementById('offlineWebSocketResult').innerHTML = '<div class="error">请选择音频文件</div>';
        return;
    }

    if (isOfflineProcessing) {
        document.getElementById('offlineWebSocketResult').innerHTML = '<div class="warning">正在处理中，请稍候...</div>';
        return;
    }

    // 更新UI状态
    document.getElementById('startOfflineBtn').disabled = true;
    document.getElementById('cancelOfflineBtn').disabled = false;
    document.getElementById('offlineWebSocketResult').innerHTML = '<div class="info">🔄 正在连接WebSocket...</div>';

    // 创建Socket连接
    offlineSocket = io();

    // 连接成功
    offlineSocket.on('connect', function() {
        console.log('✅ WebSocket连接成功');
        document.getElementById('offlineWebSocketResult').innerHTML = '<div class="info">🔄 正在准备上传...</div>';

        // 开始上传
        uploadAudioFile(audioFile, enableSpeaker);
    });

    // 连接失败
    offlineSocket.on('connect_error', function(error) {
        console.error('❌ WebSocket连接失败:', error);
        document.getElementById('offlineWebSocketResult').innerHTML = '<div class="error">❌ WebSocket连接失败</div>';
        resetOfflineUI();
    });

    // 离线转录开始
    offlineSocket.on('offline_started', function(data) {
        console.log('📁 离线转录会话已创建:', data);
        isOfflineProcessing = true;
    });

    // 上传进度
    offlineSocket.on('upload_progress', function(data) {
        console.log('📤 上传进度:', data.data.progress);
        updateProgress(data.data.progress, `上传进度: ${data.data.progress.toFixed(1)}%`);
    });

    // 处理状态
    offlineSocket.on('processing_status', function(data) {
        console.log('🔄 处理状态:', data.message);
        updateProgress(data.data.progress, data.message);
    });

    // 转录结果
    offlineSocket.on('offline_result', function(data) {
        console.log('✅ 收到转录结果:', data);  // 添加详细日志

        if (data && data.data) {
            console.log('📝 转录文本:', data.data.voice_text);
            console.log('🎭 分段结果:', data.data.segmented_text);
            displayOfflineResult(data.data);
        } else {
            console.error('❌ 转录结果数据格式错误:', data);
            document.getElementById('offlineWebSocketResult').innerHTML =
                '<div class="error">❌ 转录结果数据格式错误</div>';
        }
        resetOfflineUI();
    });

    // 错误处理 - 添加更详细的错误信息
    offlineSocket.on('offline_error', function(data) {
        console.error('❌ 转录错误:', data);
        document.getElementById('offlineWebSocketResult').innerHTML =
            `<div class="error">❌ 转录失败: ${data.message || '未知错误'}</div>`;
        resetOfflineUI();
    });
}

function uploadAudioFile(file, enableSpeaker) {
    console.log('📤 开始上传文件:', file.name, '大小:', file.size);

    const chunkSize = 1024 * 1024; // 1MB chunks
    const totalChunks = Math.ceil(file.size / chunkSize);

    // 发送开始信号
    offlineSocket.emit('start_offline', {
        filename: file.name,
        file_size: file.size,
        enable_speaker: enableSpeaker,
        chunk_size: chunkSize,
        total_chunks: totalChunks
    });

    // 分块上传
    let currentChunk = 0;

    function uploadNextChunk() {
        if (currentChunk >= totalChunks) {
            console.log('✅ 所有块上传完成');
            return;
        }

        const start = currentChunk * chunkSize;
        const end = Math.min(start + chunkSize, file.size);
        const chunk = file.slice(start, end);
        const isLast = currentChunk === totalChunks - 1;

        const reader = new FileReader();
        reader.onload = function(e) {
            const arrayBuffer = e.target.result;
            const uint8Array = new Uint8Array(arrayBuffer);

            console.log(`📤 上传块 ${currentChunk + 1}/${totalChunks}`);

            offlineSocket.emit('upload_chunk', {
                index: currentChunk,
                chunk: Array.from(uint8Array),
                is_last: isLast
            });

            currentChunk++;

            // 继续上传下一块
            if (!isLast) {
                setTimeout(uploadNextChunk, 100); // 小延迟避免过载
            }
        };

        reader.readAsArrayBuffer(chunk);
    }

    uploadNextChunk();
}

function updateProgress(progress, message) {
    // 显示进度条
    const progressContainer = document.getElementById('offlineProgress');
    const progressBar = document.getElementById('offlineProgressBar');
    const progressText = document.getElementById('offlineProgressText');

    progressContainer.style.display = 'block';
    progressText.style.display = 'block';

    progressBar.style.width = `${progress}%`;
    progressText.textContent = message;

    // 更新结果区域
    document.getElementById('offlineWebSocketResult').innerHTML = `<div class="info">🔄 ${message}</div>`;
}

function displayOfflineResult(data) {
    console.log('📋 显示离线转录结果:', data);  // 添加调试日志

    let html = '<div class="success">✅ 离线转录完成</div>';

    // 检查是否有说话人分离结果
    if (data.segmented_text && Array.isArray(data.segmented_text) && data.segmented_text.length > 0) {
        html += '<div class="speaker-result">';
        html += '<h3>🎭 说话人分离结果:</h3>';

        data.segmented_text.forEach(segment => {
            const speakerClass = segment.speaker.replace('_', '-').toLowerCase();
            html += `<div class="segment ${speakerClass}">
                <span class="speaker-tag">${segment.speaker}</span>
                <span class="time-tag">(${segment.start_time.toFixed(1)}s-${segment.end_time.toFixed(1)}s)</span>:
                ${segment.text}
            </div>`;
        });
        html += '</div>';
    } else if (data.voice_text && data.voice_text.trim()) {
        // 显示普通转录结果
        html += `<p><strong>📝 转录结果:</strong></p><p>${data.voice_text}</p>`;
    } else {
        // 没有转录结果
        html += '<p><strong>⚠️ 没有检测到语音内容</strong></p>';
        console.warn('⚠️ 转录结果为空:', data);
    }

    if (data.duration) {
        html += `<p><strong>⏱️ 音频时长:</strong> ${data.duration.toFixed(1)}秒</p>`;
    }

    if (data.filename) {
        html += `<p><strong>📁 文件名:</strong> ${data.filename}</p>`;
    }

    document.getElementById('offlineWebSocketResult').innerHTML = html;
}

function cancelOfflineWebSocket() {
    console.log('🚫 取消离线转录');

    if (offlineSocket) {
        offlineSocket.emit('cancel_offline');
        offlineSocket.disconnect();
        offlineSocket = null;
    }

    resetOfflineUI();
}

function resetOfflineUI() {
    isOfflineProcessing = false;
    document.getElementById('startOfflineBtn').disabled = false;
    document.getElementById('cancelOfflineBtn').disabled = true;

    // 隐藏进度条
    document.getElementById('offlineProgress').style.display = 'none';
    document.getElementById('offlineProgressText').style.display = 'none';

    if (offlineSocket) {
        offlineSocket.disconnect();
        offlineSocket = null;
    }
}

// 页面卸载时清理
window.addEventListener('beforeunload', function() {
    if (offlineSocket) {
        offlineSocket.disconnect();
    }
});