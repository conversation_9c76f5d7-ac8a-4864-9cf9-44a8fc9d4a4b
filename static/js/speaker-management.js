/**
 * 人员管理功能 JavaScript
 * 处理录音、上传、识别等功能
 */

// 录音相关功能
async function startRecording() {
    try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        
        recordedChunks = [];
        mediaRecorder = new MediaRecorder(stream);
        
        mediaRecorder.ondataavailable = function(event) {
            if (event.data.size > 0) {
                recordedChunks.push(event.data);
            }
        };
        
        mediaRecorder.onstop = function() {
            recordedBlob = new Blob(recordedChunks, { type: 'audio/wav' });
            
            // 创建音频URL用于播放
            const audioURL = URL.createObjectURL(recordedBlob);
            const audioPlayback = document.getElementById('audioPlayback');
            audioPlayback.src = audioURL;
            audioPlayback.style.display = 'block';
            
            // 更新UI
            document.getElementById('playRecordBtn').disabled = false;
            document.getElementById('uploadRecordBtn').disabled = false;
            document.getElementById('recordingStatus').innerHTML = '<div class="success">✅ 录音完成，可以播放或上传</div>';
            
            // 停止所有音频轨道
            stream.getTracks().forEach(track => track.stop());
        };
        
        mediaRecorder.start();
        
        // 更新UI
        document.getElementById('startRecordBtn').disabled = true;
        document.getElementById('stopRecordBtn').disabled = false;
        document.getElementById('recordingStatus').innerHTML = '<div class="info">🎙️ 正在录音中...</div>';
        document.getElementById('recordingStatus').style.display = 'block';
        
        console.log('录音开始');
        
    } catch (error) {
        console.error('录音失败:', error);
        alert('录音失败: ' + error.message);
        resetRecordingUI();
    }
}

function stopRecording() {
    if (mediaRecorder && mediaRecorder.state !== 'inactive') {
        mediaRecorder.stop();
        console.log('录音停止');
    }
    
    // 更新UI
    document.getElementById('startRecordBtn').disabled = false;
    document.getElementById('stopRecordBtn').disabled = true;
}

function playRecording() {
    const audioPlayback = document.getElementById('audioPlayback');
    audioPlayback.play();
}

async function uploadRecording() {
    if (!recordedBlob) {
        alert('没有录音数据');
        return;
    }
    
    if (!currentSpeakerId) {
        alert('请先选择要管理的人员');
        return;
    }
    
    const formData = new FormData();
    formData.append('audio', recordedBlob, 'recording.wav');
    
    try {
        document.getElementById('recordingStatus').innerHTML = '<div class="info">🔄 正在上传录音...</div>';
        
        const response = await fetch(`/api/speakers/${currentSpeakerId}/samples`, {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.code === 200) {
            document.getElementById('recordingStatus').innerHTML = '<div class="success">✅ 录音上传成功</div>';
            loadSamples(currentSpeakerId); // 刷新样本列表
            resetRecordingUI();
        } else {
            document.getElementById('recordingStatus').innerHTML = `<div class="error">❌ 上传失败: ${result.message}</div>`;
        }
    } catch (error) {
        document.getElementById('recordingStatus').innerHTML = `<div class="error">❌ 上传失败: ${error.message}</div>`;
    }
}

// 上传音频文件
async function uploadAudioFile() {
    const fileInput = document.getElementById('sampleAudioFile');
    const file = fileInput.files[0];
    
    if (!file) {
        alert('请选择音频文件');
        return;
    }
    
    if (!currentSpeakerId) {
        alert('请先选择要管理的人员');
        return;
    }
    
    const formData = new FormData();
    formData.append('audio', file);
    
    try {
        const resultDiv = document.getElementById('sampleList');
        resultDiv.innerHTML = '<div class="info">🔄 正在上传音频文件...</div>';
        
        const response = await fetch(`/api/speakers/${currentSpeakerId}/samples`, {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.code === 200) {
            alert('✅ 音频文件上传成功');
            fileInput.value = ''; // 清空文件选择
            loadSamples(currentSpeakerId); // 刷新样本列表
        } else {
            alert(`❌ 上传失败: ${result.message}`);
            loadSamples(currentSpeakerId); // 恢复样本列表显示
        }
    } catch (error) {
        alert(`❌ 上传失败: ${error.message}`);
        loadSamples(currentSpeakerId); // 恢复样本列表显示
    }
}

// 识别说话人
async function identifySpeaker() {
    const fileInput = document.getElementById('identifyAudioFile');
    const file = fileInput.files[0];
    
    if (!file) {
        alert('请选择要识别的音频文件');
        return;
    }
    
    const formData = new FormData();
    formData.append('audio', file);
    
    const resultDiv = document.getElementById('identifyResult');
    resultDiv.innerHTML = '<div class="info">🔄 正在识别说话人...</div>';
    
    try {
        const response = await fetch('/api/speakers/identify', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.code === 200) {
            let html = '<div class="success">✅ 说话人识别完成</div>';
            
            if (result.data.identified) {
                html += `
                    <div class="speaker-result">
                        <h4>🎯 识别结果</h4>
                        <p><strong>说话人:</strong> ${result.data.speaker_name}</p>
                        <p><strong>置信度:</strong> ${(result.data.confidence * 100).toFixed(1)}%</p>
                        <p><strong>说明:</strong> ${result.data.reason}</p>
                    </div>
                `;
            } else {
                html += `
                    <div class="warning">
                        <h4>⚠️ 未识别到已注册说话人</h4>
                        <p><strong>置信度:</strong> ${(result.data.confidence * 100).toFixed(1)}%</p>
                        <p><strong>说明:</strong> ${result.data.reason}</p>
                    </div>
                `;
            }
            
            resultDiv.innerHTML = html;
        } else {
            resultDiv.innerHTML = `<div class="error">❌ 识别失败: ${result.message}</div>`;
        }
    } catch (error) {
        resultDiv.innerHTML = `<div class="error">❌ 识别失败: ${error.message}</div>`;
    }
}

// 显示统计信息
async function showSpeakerStats() {
    const resultDiv = document.getElementById('speakerList');
    resultDiv.innerHTML = '<div class="info">🔄 正在获取统计信息...</div>';
    
    try {
        const response = await fetch('/api/speakers/stats');
        const result = await response.json();
        
        if (result.code === 200) {
            const stats = result.data;
            let html = '<div class="success">✅ 统计信息获取成功</div>';
            html += `
                <div class="speaker-result">
                    <h4>📊 系统统计</h4>
                    <p><strong>活跃人员数:</strong> ${stats.active_speakers}</p>
                    <p><strong>总人员数:</strong> ${stats.total_speakers}</p>
                    <p><strong>音频样本数:</strong> ${stats.total_samples}</p>
                    <p><strong>特征向量数:</strong> ${stats.total_features}</p>
                    <p><strong>总音频时长:</strong> ${stats.total_duration_minutes.toFixed(1)} 分钟</p>
                    <p><strong>数据库路径:</strong> ${stats.database_path}</p>
                    <p><strong>样本存储目录:</strong> ${stats.audio_samples_dir}</p>
                </div>
            `;
            
            resultDiv.innerHTML = html;
        } else {
            resultDiv.innerHTML = `<div class="error">❌ 获取统计信息失败: ${result.message}</div>`;
        }
    } catch (error) {
        resultDiv.innerHTML = `<div class="error">❌ 请求失败: ${error.message}</div>`;
    }
}

// 重置录音UI
function resetRecordingUI() {
    document.getElementById('startRecordBtn').disabled = false;
    document.getElementById('stopRecordBtn').disabled = true;
    document.getElementById('playRecordBtn').disabled = true;
    document.getElementById('uploadRecordBtn').disabled = true;
    document.getElementById('recordingStatus').style.display = 'none';
    document.getElementById('audioPlayback').style.display = 'none';
    
    recordedBlob = null;
    recordedChunks = [];
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查浏览器是否支持录音
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        console.warn('浏览器不支持录音功能');
        document.getElementById('startRecordBtn').disabled = true;
        document.getElementById('startRecordBtn').textContent = '不支持录音';
    }
    
    console.log('✅ 人员管理功能已加载');
});
