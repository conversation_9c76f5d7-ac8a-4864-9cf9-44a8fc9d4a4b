body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f5f5f5;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    background-color: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

h1 {
    color: #333;
    text-align: center;
    margin-bottom: 30px;
    border-bottom: 3px solid #007bff;
    padding-bottom: 10px;
}

.section {
    margin-bottom: 40px;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background-color: #fafafa;
}

.section h2 {
    color: #007bff;
    margin-top: 0;
    margin-bottom: 20px;
    font-size: 1.5em;
}

.form-group {
    margin-bottom: 15px;
}

label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #555;
}

input[type="file"] {
    width: 100%;
    padding: 10px;
    border: 2px dashed #007bff;
    border-radius: 5px;
    background-color: white;
    cursor: pointer;
}

input[type="checkbox"] {
    margin-right: 8px;
    transform: scale(1.2);
}

button {
    background-color: #007bff;
    color: white;
    padding: 12px 24px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    margin-right: 10px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
}

button:hover:not(:disabled) {
    background-color: #0056b3;
    transform: translateY(-1px);
}

button:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
    opacity: 0.6;
    transform: none;
}

#stopBtn, #cancelOfflineBtn {
    background-color: #dc3545;
}

#stopBtn:hover:not(:disabled), #cancelOfflineBtn:hover:not(:disabled) {
    background-color: #c82333;
}

#clearBtn {
    background-color: #6c757d;
}

#clearBtn:hover:not(:disabled) {
    background-color: #545b62;
}

#clearBtn:disabled {
    background-color: #adb5bd;
    opacity: 0.5;
}

.result {
    margin-top: 20px;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background-color: white;
    min-height: 100px;
    max-height: 400px;
    overflow-y: auto;
    white-space: pre-wrap;
    font-family: 'Courier New', monospace;
}

.loading {
    color: #007bff;
    font-style: italic;
}

.error {
    color: #dc3545;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    padding: 10px;
    border-radius: 5px;
    margin: 10px 0;
}

.success {
    color: #155724;
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    padding: 10px;
    border-radius: 5px;
    margin: 10px 0;
}

.info {
    color: #0c5460;
    background-color: #d1ecf1;
    border: 1px solid #bee5eb;
    padding: 10px;
    border-radius: 5px;
    margin: 10px 0;
}

.warning {
    color: #856404;
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    padding: 10px;
    border-radius: 5px;
    margin: 10px 0;
}

/* 说话人分离样式 */
.speaker-result {
    border-left: 3px solid #007bff;
    padding-left: 10px;
    margin: 10px 0;
}

.segment {
    margin: 5px 0;
    padding: 8px;
    border-radius: 5px;
    background-color: #f8f9fa;
    word-wrap: break-word;
    border-left: 3px solid #ddd;
    transition: all 0.3s ease;
}

.segment:hover {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.segment.speaker-a {
    background-color: #e3f2fd;
    border-left: 3px solid #2196f3;
}

.segment.speaker-b {
    background-color: #f3e5f5;
    border-left: 3px solid #9c27b0;
}

.segment.speaker-0 {
    background-color: #e8f5e8;
    border-left: 3px solid #4caf50;
}

.segment.speaker-1 {
    background-color: #fff3e0;
    border-left: 3px solid #ff9800;
}

.segment.speaker-2 {
    background-color: #f3e5f5;
    border-left: 3px solid #9c27b0;
}

.segment.speaker-3 {
    background-color: #e0f2f1;
    border-left: 3px solid #009688;
}

.speaker-tag {
    font-weight: bold;
    color: #333;
    background-color: rgba(0,0,0,0.1);
    padding: 2px 6px;
    border-radius: 3px;
    margin-right: 5px;
}

.time-tag {
    font-size: 0.9em;
    color: #666;
    margin-right: 5px;
}

.timestamp-tag {
    font-size: 0.8em;
    color: #666;
    margin-right: 5px;
    background-color: #e9ecef;
    padding: 1px 4px;
    border-radius: 3px;
}

/* 实时转录历史记录样式 */
.realtime-history {
    max-height: 350px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 10px;
    margin: 10px 0;
    background-color: #f9f9f9;
}

.simple-segment {
    margin: 5px 0;
    padding: 8px;
    background-color: #e3f2fd;
    border-left: 3px solid #2196f3;
    border-radius: 3px;
    word-wrap: break-word;
    transition: all 0.3s ease;
}

.simple-segment:hover {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.realtime-stats {
    text-align: center;
    margin-top: 10px;
    color: #666;
    font-style: italic;
    font-size: 0.9em;
}

/* 进度条样式 */
.progress-container {
    width: 100%;
    background-color: #f0f0f0;
    border-radius: 4px;
    margin: 10px 0;
    height: 20px;
}

.progress-fill {
    height: 100%;
    background-color: #007bff;
    border-radius: 4px;
    transition: width 0.3s ease;
    width: 0%;
}

/* API信息样式 */
.api-info {
    background-color: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
}

.api-info h3 {
    margin-top: 0;
    color: #0056b3;
}

.api-info code {
    background-color: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 15px;
        margin: 10px;
    }

    button {
        width: 100%;
        margin-bottom: 10px;
    }

    .section {
        padding: 15px;
    }

    .realtime-history {
        max-height: 250px;
    }
}

/* 滚动条样式 */
.result::-webkit-scrollbar,
.realtime-history::-webkit-scrollbar {
    width: 8px;
}

.result::-webkit-scrollbar-track,
.realtime-history::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.result::-webkit-scrollbar-thumb,
.realtime-history::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

.result::-webkit-scrollbar-thumb:hover,
.realtime-history::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* 动画效果 */
.section {
    transition: box-shadow 0.3s ease;
}

.section:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* 文件上传区域样式 */
input[type="file"]:hover {
    border-color: #0056b3;
    background-color: #f8f9fa;
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 5px;
}

.status-connected {
    background-color: #28a745;
}

.status-disconnected {
    background-color: #dc3545;
}

.status-processing {
    background-color: #ffc107;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.segment, .simple-segment {
    animation: fadeIn 0.3s ease-out;
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 按钮状态增强 */
button:active:not(:disabled) {
    transform: translateY(0);
}

/* 清空按钮特殊样式 */
#clearBtn:enabled {
    background-color: #6c757d;
}

#clearBtn:enabled:hover {
    background-color: #545b62;
}