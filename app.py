#!/usr/bin/env python3
"""
音频转文字服务 - 主应用
基于原有main.py重构，分离离线和实时转录服务
"""

import os
import json
import numpy as np
import soundfile as sf
import tempfile
import subprocess
import time
import io
import threading
from datetime import datetime, timedelta
from pathlib import Path
from flask import Flask, request, jsonify, render_template
from flask_socketio import SocketIO, emit

from config import SERVER_CONFIG, REALTIME_CONFIG, SESSION_CONFIG
from services.offline_transcription import OfflineAudioTranscriptionService
from services.realtime_transcription import RealtimeAudioTranscriptionService
from services.audio_processor import AudioProcessingManager
from services.speaker_enrollment import SpeakerEnrollmentService

# 在任何其他导入之前执行修复
print("正在修复ModelScope和datasets兼容性...")


def emergency_fix():
    import datasets.exceptions
    exceptions = [
        'DataFilesNotFoundError', 'DatasetNotFoundError', 'EmptyDatasetError',
        'NonMatchingChecksumError', 'UnexpectedDownloadedFileError',
        'DatasetGenerationError', 'DatasetBuildError', 'DatasetInfoMissingError',
        'ExpectedMoreDownloadedFiles', 'ExpectedMoreSplits', 'SplitsNotFoundError'
    ]
    for exc in exceptions:
        if not hasattr(datasets.exceptions, exc):
            setattr(datasets.exceptions, exc, type(exc, (Exception,), {}))
    print(f"✓ 修复了 {len(exceptions)} 个异常类")


emergency_fix()


def convert_audio_to_wav(audio_file):
    """转换音频文件为WAV格式"""
    try:
        # 创建临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix='.tmp') as temp_input:
            audio_file.seek(0)
            temp_input.write(audio_file.read())
            temp_input_path = temp_input.name

        with tempfile.NamedTemporaryFile(delete=False, suffix='.wav') as temp_wav:
            temp_wav_path = temp_wav.name

        # 使用ffmpeg转换
        cmd = [
            'ffmpeg', '-i', temp_input_path,
            '-ar', '16000', '-ac', '1', '-f', 'wav', '-y', temp_wav_path
        ]

        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode != 0:
            raise Exception(f"ffmpeg转换失败: {result.stderr}")

        # 读取转换后的音频
        audio_data, sample_rate = sf.read(temp_wav_path)

        # 清理临时文件
        os.unlink(temp_input_path)
        os.unlink(temp_wav_path)

        return audio_data, sample_rate

    except Exception as e:
        # 清理可能存在的临时文件
        for path in [temp_input_path, temp_wav_path]:
            if 'path' in locals() and os.path.exists(path):
                os.unlink(path)
        raise e


# Flask应用
app = Flask(__name__)
app.config['SECRET_KEY'] = SERVER_CONFIG['secret_key']
app.config['MAX_CONTENT_LENGTH'] = SERVER_CONFIG['max_content_length']
socketio = SocketIO(app, cors_allowed_origins="*", max_http_buffer_size=SERVER_CONFIG['max_content_length'])

# 全局服务实例
offline_service = None
realtime_service = None
audio_processor = None
speaker_service = None
realtime_sessions = {}
session_cleanup_thread = None


def start_session_cleanup():
    """启动会话清理线程"""
    global session_cleanup_thread

    def cleanup_worker():
        while True:
            try:
                current_time = time.time()
                expired_sessions = []

                # 查找过期会话
                for session_id, session in realtime_sessions.items():
                    last_activity = session.get('last_activity', current_time)
                    idle_time = current_time - last_activity

                    if idle_time > SESSION_CONFIG['max_idle_time']:
                        expired_sessions.append(session_id)

                # 清理过期会话
                for session_id in expired_sessions:
                    cleanup_session(session_id, reason="会话超时")

                # 检查并发会话数
                if len(realtime_sessions) > SESSION_CONFIG['max_concurrent_sessions']:
                    # 清理最老的会话
                    oldest_sessions = sorted(
                        realtime_sessions.items(),
                        key=lambda x: x[1].get('last_activity', 0)
                    )

                    excess_count = len(realtime_sessions) - SESSION_CONFIG['max_concurrent_sessions']
                    for i in range(excess_count):
                        session_id = oldest_sessions[i][0]
                        cleanup_session(session_id, reason="超出并发限制")

                if realtime_sessions:
                    print(f"📊 当前活跃会话: {len(realtime_sessions)}")

            except Exception as e:
                print(f"❌ 会话清理异常: {e}")

            time.sleep(SESSION_CONFIG['cleanup_interval'])

    session_cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True, name="SessionCleanup")
    session_cleanup_thread.start()
    print("🧹 会话清理线程已启动")


def cleanup_session(session_id, reason="手动清理"):
    """清理指定会话"""
    try:
        if session_id in realtime_sessions:
            session = realtime_sessions[session_id]

            # 清理缓冲区
            if 'buffer' in session:
                del session['buffer']

            # 清理音频块（离线WebSocket）
            if 'audio_chunks' in session:
                del session['audio_chunks']

            # 从会话列表中移除
            del realtime_sessions[session_id]

            # 通知客户端
            socketio.emit('session_expired', {
                "code": 200,
                "message": f"会话已关闭: {reason}",
                "data": {"reason": reason}
            }, room=session_id)

            print(f"🧹 会话已清理: {session_id} ({reason})")
            return True
    except Exception as e:
        print(f"❌ 清理会话失败: {session_id}, 错误: {e}")

    return False


def update_session_activity(session_id):
    """更新会话活动时间"""
    if session_id in realtime_sessions:
        realtime_sessions[session_id]['last_activity'] = time.time()


@app.route('/')
def index():
    """测试页面 - 使用模板"""
    return render_template('index.html')


@app.route('/transcribe', methods=['POST'])
def transcribe():
    """文件转录接口 - 使用离线服务"""
    try:
        if 'audio' not in request.files:
            return jsonify({
                "code": 400,
                "message": "没有上传音频文件",
                "data": None
            }), 400

        audio_file = request.files['audio']
        if audio_file.filename == '':
            return jsonify({
                "code": 400,
                "message": "文件名为空",
                "data": None
            }), 400

        # 检查文件大小
        audio_file.seek(0, 2)
        file_size = audio_file.tell()
        audio_file.seek(0)

        if file_size == 0:
            return jsonify({
                "code": 400,
                "message": "文件为空",
                "data": None
            }), 400

        print(f"接收到文件: {audio_file.filename}, 大小: {file_size / 1024 / 1024:.2f}MB")

        # 检查文件扩展名
        allowed_extensions = {'.wav', '.mp3', '.m4a', '.aac', '.flac', '.ogg', '.wma', '.mp4', '.avi', '.mov'}
        file_ext = Path(audio_file.filename).suffix.lower()

        if file_ext not in allowed_extensions:
            return jsonify({
                "code": 400,
                "message": f"不支持的文件格式 '{file_ext}'",
                "data": {"supported_formats": list(allowed_extensions)}
            }), 400

        # 获取参数
        enable_speaker = request.form.get('enable_speaker', 'false').lower() == 'true'
        selected_speakers = request.form.get('selected_speakers', '')  # 新增：选择的说话人ID列表
        print(f"启用说话人分离: {enable_speaker}")

        # 解析选择的说话人列表
        selected_speaker_ids = []
        if enable_speaker and selected_speakers:
            try:
                import json
                selected_speaker_ids = json.loads(selected_speakers) if isinstance(selected_speakers, str) else selected_speakers
                print(f"选择的说话人: {selected_speaker_ids}")
            except (json.JSONDecodeError, TypeError) as e:
                print(f"解析选择的说话人列表失败: {e}")
                selected_speaker_ids = []

        # 转换音频格式
        try:
            audio_data, sample_rate = convert_audio_to_wav(audio_file)
        except Exception as e:
            return jsonify({
                "code": 400,
                "message": f"音频格式转换失败: {str(e)}",
                "data": None
            }), 400

        # 使用离线服务进行转录
        if enable_speaker:
            # 如果指定了特定的说话人，使用定向识别
            if selected_speaker_ids and speaker_service:
                result = offline_service.transcribe_audio_with_targeted_speaker_identification(
                    audio_data, sample_rate, selected_speaker_ids
                )
            else:
                # 使用通用说话人分离
                result = offline_service.transcribe_audio_with_speaker(audio_data, sample_rate)

            if "error" in result:
                return jsonify({
                    "code": 500,
                    "message": result["error"],
                    "data": None
                }), 500

            segmented_text = []
            for segment in result.get("segments", []):
                segmented_text.append({
                    "start_time": segment.get("start", 0),
                    "end_time": segment.get("end", 0),
                    "speaker": segment.get("speaker", "Speaker_0"),
                    "text": segment.get("text", "")
                })

            return jsonify({
                "code": 200,
                "message": "转录成功",
                "data": {
                    "voice_text": result.get("text", ""),
                    "segmented_text": segmented_text,
                    "selected_speakers": selected_speaker_ids  # 返回选择的说话人信息
                }
            })
        else:
            result = offline_service.transcribe_audio_simple(audio_data, sample_rate)

            if "error" in result:
                return jsonify({
                    "code": 500,
                    "message": result["error"],
                    "data": None
                }), 500

            return jsonify({
                "code": 200,
                "message": "转录成功",
                "data": {
                    "voice_text": result.get("text", "")
                }
            })

    except Exception as e:
        print(f"❌ 转录过程发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({
            "code": 500,
            "message": f"服务器内部错误: {str(e)}",
            "data": None
        }), 500


@socketio.on('connect')
def handle_connect():
    session_id = request.sid
    print(f"🔗 客户端连接: {session_id}")

    # 检查并发限制
    if len(realtime_sessions) >= SESSION_CONFIG['max_concurrent_sessions']:
        emit('connection_rejected', {
            "code": 429,
            "message": "服务器繁忙，请稍后重试",
            "data": {"max_sessions": SESSION_CONFIG['max_concurrent_sessions']}
        })
        return False

    # 发送心跳配置
    emit('heartbeat_config', {
        "interval": SESSION_CONFIG['heartbeat_interval'] * 1000,  # 转换为毫秒
        "max_idle_time": SESSION_CONFIG['max_idle_time'] * 1000
    })


@socketio.on('disconnect')
def handle_disconnect():
    session_id = request.sid
    cleanup_session(session_id, reason="客户端断开")


@socketio.on('heartbeat')
def handle_heartbeat():
    """处理客户端心跳"""
    session_id = request.sid
    update_session_activity(session_id)
    emit('heartbeat_ack', {"timestamp": time.time()})


@socketio.on('start_realtime')
def handle_start_realtime(data):
    """开始实时转录 - 添加会话管理和说话人选择支持"""
    session_id = request.sid
    enable_speaker = data.get('enable_speaker', False)
    selected_speakers = data.get('selected_speakers', [])  # 新增：选择的说话人ID列表

    # 检查会话是否已存在
    if session_id in realtime_sessions:
        emit('realtime_error', {"code": 400, "message": "会话已存在"})
        return

    # 验证选择的说话人
    valid_speakers = []
    if enable_speaker and selected_speakers and speaker_service:
        for speaker_id in selected_speakers:
            speaker_info = speaker_service.get_speaker_info(speaker_id)
            if speaker_info and speaker_info.get('feature_count', 0) > 0:
                valid_speakers.append({
                    'speaker_id': speaker_id,
                    'name': speaker_info['name']
                })
        print(f"实时转录选择的有效说话人: {valid_speakers}")

    realtime_sessions[session_id] = {
        'type': 'realtime',
        'buffer': [],
        'sample_rate': REALTIME_CONFIG['sample_rate'],
        'enable_speaker': enable_speaker,
        'selected_speakers': [s['speaker_id'] for s in valid_speakers],  # 新增：存储选择的说话人ID
        'selected_speaker_info': valid_speakers,  # 新增：存储说话人详细信息
        'last_process_time': time.time(),
        'last_activity': time.time(),
        'created_at': time.time(),
        'audio_chunks_received': 0,
        'total_audio_duration': 0
    }

    # 启动异步处理器
    audio_processor.start_processing()

    emit('realtime_started', {
        "code": 200,
        "message": "实时转录已开始（异步处理模式）",
        "data": {
            "session_id": session_id,
            "max_idle_time": SESSION_CONFIG['max_idle_time'],
            "heartbeat_interval": SESSION_CONFIG['heartbeat_interval'],
            "enable_speaker": enable_speaker,
            "selected_speakers": valid_speakers
        }
    })


@socketio.on('audio_chunk')
def handle_audio_chunk(data):
    """处理实时音频块 - 修复数据类型"""
    session_id = request.sid

    if session_id not in realtime_sessions:
        emit('realtime_error', {"code": 400, "message": "会话未初始化"})
        return

    session = realtime_sessions[session_id]
    if session.get('type') != 'realtime':
        emit('realtime_error', {"code": 400, "message": "会话类型错误"})
        return

    # 更新会话活动时间
    update_session_activity(session_id)

    try:
        audio_data = data.get('audio_data')  # 修改为 audio_data
        if not audio_data:
            return

        session['audio_chunks_received'] += 1

        # 解码音频块 - 修复数据类型转换
        try:
            if isinstance(audio_data, list):
                # 从数组转换为numpy数组
                audio_array = np.array(audio_data, dtype=np.float32)
            else:
                # 如果是字节数据
                audio_array = np.frombuffer(audio_data, dtype=np.float32)

            if len(audio_array) == 0:
                return

            session['buffer'].extend(audio_array)
            session['total_audio_duration'] += len(audio_array) / session['sample_rate']

            # 每100个音频块打印一次统计
            if session['audio_chunks_received'] % 100 == 0:
                print(
                    f"📊 会话统计 {session_id}: 音频块={session['audio_chunks_received']}, 总时长={session['total_audio_duration']:.1f}s")

        except Exception as e:
            print(f"音频块解析错误: {e}")
            return

        # 检查缓冲区大小，防止内存溢出
        max_buffer_samples = int(session['sample_rate'] * REALTIME_CONFIG['max_buffer_duration'])
        if len(session['buffer']) > max_buffer_samples:
            print(f"⚠️ 缓冲区过大，清理旧数据")
            session['buffer'] = session['buffer'][-max_buffer_samples:]

        # 按配置的间隔处理
        current_time = time.time()
        if current_time - session['last_process_time'] >= REALTIME_CONFIG['process_interval']:
            if len(session['buffer']) > 0:
                buffer_array = np.array(session['buffer'])

                # 确保有足够的音频长度
                min_samples = int(session['sample_rate'] * REALTIME_CONFIG['min_audio_duration'])
                if len(buffer_array) < min_samples:
                    print(f"缓冲区音频太短({len(buffer_array)}/{min_samples})，等待更多数据")
                    return

                # 添加到异步处理队列
                session_config = {
                    'enable_speaker': session['enable_speaker'],
                    'sample_rate': session['sample_rate']
                }

                if audio_processor.add_audio_task(session_id, buffer_array, session_config):
                    # 保留更多上下文，避免截断
                    overlap_samples = int(session['sample_rate'] * REALTIME_CONFIG['overlap_duration'])
                    keep_samples = int(session['sample_rate'] * REALTIME_CONFIG['context_duration'])
                    total_keep = max(overlap_samples, keep_samples)

                    if len(session['buffer']) > total_keep:
                        session['buffer'] = session['buffer'][-total_keep:]

            session['last_process_time'] = current_time

    except Exception as e:
        print(f"处理音频块时出错: {str(e)}")
        emit('realtime_error', {"code": 500, "message": str(e)})


@socketio.on('stop_realtime')
def handle_stop_realtime():
    """停止实时转录 - 添加统计信息"""
    session_id = request.sid

    if session_id in realtime_sessions:
        session = realtime_sessions[session_id]

        if session.get('type') != 'realtime':
            emit('realtime_error', {"code": 400, "message": "会话类型错误"})
            return

        # 打印会话统计
        session_duration = time.time() - session.get('created_at', time.time())
        print(f"📊 会话结束统计:")
        print(f"  - 会话ID: {session_id}")
        print(f"  - 持续时间: {session_duration / 60:.1f}分钟")
        print(f"  - 音频块数: {session.get('audio_chunks_received', 0)}")
        print(f"  - 总音频时长: {session.get('total_audio_duration', 0):.1f}秒")

        # 处理剩余音频
        if len(session['buffer']) > 0:
            buffer_array = np.array(session['buffer'])

            if session['enable_speaker']:
                result = realtime_service.transcribe_audio_with_speaker(
                    buffer_array,
                    session['sample_rate']
                )

                if result.get('text', '').strip():
                    segmented_text = []
                    for segment in result.get("segments", []):
                        segmented_text.append({
                            "start_time": segment.get("start", 0),
                            "end_time": segment.get("end", 0),
                            "speaker": segment.get("speaker", "Speaker_0"),
                            "text": segment.get("text", "")
                        })

                    emit('realtime_result', {
                        "code": 200,
                        "message": "最终转录结果",
                        "data": {
                            "voice_text": result.get("text", ""),
                            "segmented_text": segmented_text
                        }
                    })
            else:
                result = realtime_service.transcribe_audio_simple(
                    buffer_array,
                    session['sample_rate']
                )

                if result.get('text', '').strip():
                    emit('realtime_result', {
                        "code": 200,
                        "message": "最终转录结果",
                        "data": {
                            "voice_text": result.get("text", "")
                        }
                    })

        # 清理会话
        cleanup_session(session_id, reason="用户停止")

        emit('realtime_stopped', {
            "code": 200,
            "message": "实时转录已停止",
            "data": None
        })


@socketio.on('start_offline')
def handle_start_offline(data):
    """开始离线转录 - WebSocket版本"""
    session_id = request.sid

    try:
        # 检查会话是否已存在
        if session_id in realtime_sessions:
            emit('offline_error', {"code": 400, "message": "会话已存在，请先结束当前会话"})
            return

        # 获取参数
        enable_speaker = data.get('enable_speaker', False)
        selected_speakers = data.get('selected_speakers', [])  # 新增：选择的说话人ID列表
        chunk_size = data.get('chunk_size', 1024 * 1024)  # 默认1MB分块

        # 验证选择的说话人
        valid_speakers = []
        if enable_speaker and selected_speakers and speaker_service:
            for speaker_id in selected_speakers:
                speaker_info = speaker_service.get_speaker_info(speaker_id)
                if speaker_info and speaker_info.get('feature_count', 0) > 0:
                    valid_speakers.append({
                        'speaker_id': speaker_id,
                        'name': speaker_info['name']
                    })
            print(f"离线转录选择的有效说话人: {valid_speakers}")

        # 创建离线会话
        realtime_sessions[session_id] = {
            'type': 'offline',
            'enable_speaker': enable_speaker,
            'selected_speakers': [s['speaker_id'] for s in valid_speakers],  # 新增：存储选择的说话人ID
            'selected_speaker_info': valid_speakers,  # 新增：存储说话人详细信息
            'chunk_size': chunk_size,
            'audio_chunks': [],
            'total_size': 0,
            'expected_size': data.get('file_size', 0),
            'filename': data.get('filename', 'unknown'),
            'last_activity': time.time(),
            'created_at': time.time()
        }

        emit('offline_started', {
            "code": 200,
            "message": "离线转录会话已创建",
            "data": {
                "session_id": session_id,
                "chunk_size": chunk_size,
                "enable_speaker": enable_speaker,
                "selected_speakers": valid_speakers
            }
        })

        print(f"📁 离线转录会话创建: {session_id}, 文件: {data.get('filename')}")

    except Exception as e:
        print(f"❌ 创建离线会话失败: {str(e)}")
        emit('offline_error', {"code": 500, "message": f"创建会话失败: {str(e)}"})


@socketio.on('upload_chunk')
def handle_upload_chunk(data):
    """处理文件分块上传"""
    session_id = request.sid

    if session_id not in realtime_sessions:
        emit('offline_error', {"code": 400, "message": "会话不存在"})
        return

    session = realtime_sessions[session_id]
    if session.get('type') != 'offline':
        emit('offline_error', {"code": 400, "message": "会话类型错误"})
        return

    try:
        # 更新活动时间
        update_session_activity(session_id)

        chunk_data = data.get('chunk')
        chunk_index = data.get('index', 0)
        is_last = data.get('is_last', False)

        if not chunk_data:
            emit('offline_error', {"code": 400, "message": "音频块数据为空"})
            return

        # 存储音频块
        session['audio_chunks'].append({
            'index': chunk_index,
            'data': chunk_data,
            'size': len(chunk_data)
        })

        session['total_size'] += len(chunk_data)

        # 计算上传进度
        progress = 0
        if session['expected_size'] > 0:
            progress = min(100, (session['total_size'] / session['expected_size']) * 100)

        # 发送上传进度
        emit('upload_progress', {
            "code": 200,
            "message": "上传进度",
            "data": {
                "chunk_index": chunk_index,
                "total_size": session['total_size'],
                "expected_size": session['expected_size'],
                "progress": round(progress, 1),
                "is_last": is_last
            }
        })

        print(f"📤 收到音频块: {session_id}, 块{chunk_index}, 大小: {len(chunk_data)}, 进度: {progress:.1f}%")

        # 如果是最后一块，开始处理
        if is_last:
            process_offline_audio(session_id)

    except Exception as e:
        print(f"❌ 处理音频块失败: {str(e)}")
        emit('offline_error', {"code": 500, "message": f"处理音频块失败: {str(e)}"})


def process_offline_audio(session_id):
    """处理离线音频"""
    if session_id not in realtime_sessions:
        return

    session = realtime_sessions[session_id]

    try:
        # 发送处理状态
        socketio.emit('processing_status', {
            "code": 200,
            "message": "正在合并音频块...",
            "data": {"stage": "assembling", "progress": 10}
        }, room=session_id)

        # 合并音频块 - 修复数据类型转换
        session['audio_chunks'].sort(key=lambda x: x['index'])

        # 将数组转换为字节
        audio_bytes_list = []
        for chunk in session['audio_chunks']:
            chunk_data = chunk['data']
            if isinstance(chunk_data, list):
                # 从数组转换为字节
                chunk_bytes = bytes(chunk_data)
            else:
                chunk_bytes = chunk_data
            audio_bytes_list.append(chunk_bytes)

        audio_data = b''.join(audio_bytes_list)

        socketio.emit('processing_status', {
            "code": 200,
            "message": "正在转换音频格式...",
            "data": {"stage": "converting", "progress": 30}
        }, room=session_id)

        # 保存临时文件并转换
        with tempfile.NamedTemporaryFile(delete=False, suffix='.tmp') as temp_input:
            temp_input.write(audio_data)
            temp_input_path = temp_input.name

        with tempfile.NamedTemporaryFile(delete=False, suffix='.wav') as temp_wav:
            temp_wav_path = temp_wav.name

        # 使用ffmpeg转换音频格式
        cmd = [
            'ffmpeg', '-i', temp_input_path,
            '-ar', '16000', '-ac', '1', '-f', 'wav', '-y', temp_wav_path
        ]

        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode != 0:
            raise Exception(f"ffmpeg转换失败: {result.stderr}")

        # 读取转换后的音频
        audio_array, sample_rate = sf.read(temp_wav_path)

        socketio.emit('processing_status', {
            "code": 200,
            "message": "正在进行语音识别...",
            "data": {"stage": "transcribing", "progress": 60}
        }, room=session_id)

        # 调用转录服务
        if session['enable_speaker']:
            # 检查是否有选择特定的说话人
            selected_speakers = session.get('selected_speakers', [])
            if selected_speakers and speaker_service:
                # 使用定向说话人识别
                result = offline_service.transcribe_audio_with_targeted_speaker_identification(
                    audio_array, sample_rate, selected_speakers
                )
            else:
                # 使用通用说话人分离
                result = offline_service.transcribe_audio_with_speaker(audio_array, sample_rate)

            # 确保数据格式正确
            if "error" in result:
                raise Exception(result["error"])

            # 格式化说话人分离结果
            segmented_text = []
            segments = result.get("segments", [])
            if segments:
                for segment in segments:
                    segmented_text.append({
                        "start_time": segment.get("start", 0),
                        "end_time": segment.get("end", 0),
                        "speaker": segment.get("speaker", "Speaker_0"),
                        "text": segment.get("text", "")
                    })

            # 发送结果 - 确保数据完整
            result_data = {
                "voice_text": result.get('text', ''),
                "duration": result.get('duration', len(audio_array) / sample_rate),
                "filename": session['filename']
            }

            # 只有当有分段结果时才添加
            if segmented_text:
                result_data["segmented_text"] = segmented_text

            # 添加选择的说话人信息
            if selected_speakers:
                result_data["selected_speakers"] = session.get('selected_speaker_info', [])

            print(f"📤 发送离线转录结果: 文本长度={len(result_data['voice_text'])}, 分段数={len(segmented_text)}")

        else:
            result = offline_service.transcribe_audio_simple(audio_array, sample_rate)

            if "error" in result:
                raise Exception(result["error"])

            result_data = {
                "voice_text": result.get('text', ''),
                "duration": result.get('duration', len(audio_array) / sample_rate),
                "filename": session['filename']
            }

            print(f"📤 发送离线转录结果: 文本长度={len(result_data['voice_text'])}")

        # 发送最终结果
        socketio.emit('offline_result', {
            "code": 200,
            "message": "离线转录完成",
            "data": result_data
        }, room=session_id)

    except Exception as e:
        print(f"❌ 离线转录处理错误: {str(e)}")
        import traceback
        traceback.print_exc()
        socketio.emit('offline_error', {
            "code": 500,
            "message": f"处理失败: {str(e)}"
        }, room=session_id)
    finally:
        # 清理会话
        if session_id in realtime_sessions:
            cleanup_session(session_id, reason="处理完成")


@socketio.on('cancel_offline')
def handle_cancel_offline():
    """取消离线转录"""
    session_id = request.sid

    if session_id in realtime_sessions and realtime_sessions[session_id].get('type') == 'offline':
        cleanup_session(session_id, reason="用户取消")
        emit('offline_cancelled', {
            "code": 200,
            "message": "离线转录已取消",
            "data": None
        })
    else:
        emit('offline_error', {"code": 400, "message": "没有找到离线转录会话"})


@app.route('/api/sessions')
def get_sessions():
    """获取当前会话状态 - 调试接口"""
    sessions_info = []
    current_time = time.time()

    for session_id, session in realtime_sessions.items():
        idle_time = current_time - session.get('last_activity', current_time)
        session_info = {
            "session_id": session_id,
            "type": session.get('type', 'unknown'),
            "created_at": datetime.fromtimestamp(session.get('created_at', current_time)).isoformat(),
            "last_activity": datetime.fromtimestamp(session.get('last_activity', current_time)).isoformat(),
            "idle_time_minutes": idle_time / 60,
            "enable_speaker": session.get('enable_speaker', False)
        }

        # 根据会话类型添加特定信息
        if session.get('type') == 'realtime':
            session_info.update({
                "audio_chunks_received": session.get('audio_chunks_received', 0),
                "total_audio_duration": session.get('total_audio_duration', 0),
                "buffer_size": len(session.get('buffer', []))
            })
        elif session.get('type') == 'offline':
            session_info.update({
                "filename": session.get('filename', 'unknown'),
                "total_size": session.get('total_size', 0),
                "expected_size": session.get('expected_size', 0),
                "chunks_received": len(session.get('audio_chunks', []))
            })

        sessions_info.append(session_info)

    return jsonify({
        "code": 200,
        "message": "获取会话状态成功",
        "data": {
            "active_sessions": len(realtime_sessions),
            "max_concurrent_sessions": SESSION_CONFIG['max_concurrent_sessions'],
            "max_idle_time": SESSION_CONFIG['max_idle_time'],
            "sessions": sessions_info
        }
    })


# ==================== 人员管理API接口 ====================

@app.route('/api/speakers', methods=['GET'])
def list_speakers():
    """获取所有说话人列表 - 增强版本，包含详细信息"""
    try:
        if not speaker_service:
            return jsonify({
                "code": 503,
                "message": "人员识别服务未启用",
                "data": None
            }), 503

        # 获取基本说话人列表
        speakers = speaker_service.list_speakers()

        # 为每个说话人添加详细信息
        enhanced_speakers = []
        for speaker in speakers:
            speaker_id = speaker['speaker_id']

            # 获取详细信息
            speaker_info = speaker_service.get_speaker_info(speaker_id)
            if speaker_info:
                enhanced_speaker = {
                    "speaker_id": speaker_id,
                    "name": speaker['name'],
                    "description": speaker.get('description', ''),
                    "created_at": speaker.get('created_at'),
                    "updated_at": speaker.get('updated_at'),
                    "is_active": speaker.get('is_active', True),
                    "sample_count": speaker_info.get('samples', []),
                    "feature_count": speaker_info.get('feature_count', 0),
                    "total_duration": speaker_info.get('total_duration', 0),
                    "avg_quality_score": speaker_info.get('avg_quality_score', 0),
                    "has_features": speaker_info.get('feature_count', 0) > 0,
                    "ready_for_identification": speaker_info.get('feature_count', 0) >= 1  # 至少需要1个特征向量
                }

                # 处理样本信息
                samples = speaker_info.get('samples', [])
                enhanced_speaker['sample_count'] = len(samples)
                enhanced_speaker['samples'] = samples

                enhanced_speakers.append(enhanced_speaker)

        # 统计信息
        total_speakers = len(enhanced_speakers)
        ready_speakers = sum(1 for s in enhanced_speakers if s['ready_for_identification'])
        total_samples = sum(s['sample_count'] for s in enhanced_speakers)
        total_features = sum(s['feature_count'] for s in enhanced_speakers)

        return jsonify({
            "code": 200,
            "message": "获取说话人列表成功",
            "data": {
                "speakers": enhanced_speakers,
                "statistics": {
                    "total_speakers": total_speakers,
                    "ready_speakers": ready_speakers,
                    "total_samples": total_samples,
                    "total_features": total_features
                }
            }
        })
    except Exception as e:
        print(f"❌ 获取说话人列表失败: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({
            "code": 500,
            "message": f"获取说话人列表失败: {str(e)}",
            "data": None
        }), 500


@app.route('/api/speakers/available', methods=['GET'])
def get_available_speakers():
    """获取可用于识别的说话人列表（有特征向量的说话人）"""
    try:
        if not speaker_service:
            return jsonify({
                "code": 503,
                "message": "人员识别服务未启用",
                "data": None
            }), 503

        # 获取所有说话人
        speakers = speaker_service.list_speakers()

        # 筛选出有特征向量的说话人
        available_speakers = []
        for speaker in speakers:
            speaker_id = speaker['speaker_id']
            speaker_info = speaker_service.get_speaker_info(speaker_id)

            if speaker_info and speaker_info.get('feature_count', 0) > 0:
                available_speakers.append({
                    "speaker_id": speaker_id,
                    "name": speaker['name'],
                    "description": speaker.get('description', ''),
                    "sample_count": len(speaker_info.get('samples', [])),
                    "feature_count": speaker_info.get('feature_count', 0),
                    "avg_quality_score": speaker_info.get('avg_quality_score', 0)
                })

        return jsonify({
            "code": 200,
            "message": "获取可用说话人列表成功",
            "data": {
                "speakers": available_speakers,
                "count": len(available_speakers)
            }
        })
    except Exception as e:
        print(f"❌ 获取可用说话人列表失败: {e}")
        return jsonify({
            "code": 500,
            "message": f"获取可用说话人列表失败: {str(e)}",
            "data": None
        }), 500


@app.route('/api/speakers', methods=['POST'])
def create_speaker():
    """创建新的说话人 - 支持同时上传姓名和音频文件"""
    try:
        if not speaker_service:
            return jsonify({
                "code": 503,
                "message": "人员识别服务未启用",
                "data": None
            }), 503

        # 检查是否是multipart/form-data请求（包含文件上传）
        if request.content_type and 'multipart/form-data' in request.content_type:
            # 处理文件上传方式
            name = request.form.get('name', '').strip()
            description = request.form.get('description', '').strip()

            if not name:
                return jsonify({
                    "code": 400,
                    "message": "说话人姓名不能为空",
                    "data": None
                }), 400

            # 创建说话人
            speaker_id = speaker_service.enroll_speaker(name, description)

            # 检查是否有音频文件上传
            if 'audio' in request.files:
                audio_file = request.files['audio']
                if audio_file.filename != '':
                    try:
                        # 转换音频格式
                        audio_data, sample_rate = convert_audio_to_wav(audio_file)

                        # 添加音频样本并提取特征向量
                        sample_result = speaker_service.add_audio_sample(
                            speaker_id, audio_data, sample_rate,
                            metadata={"source": "initial_upload", "filename": audio_file.filename}
                        )

                        return jsonify({
                            "code": 200,
                            "message": "创建说话人并添加音频样本成功",
                            "data": {
                                "speaker_id": speaker_id,
                                "name": name,
                                "description": description,
                                "sample_added": True,
                                "sample_id": sample_result.get("sample_id"),
                                "feature_id": sample_result.get("feature_id")
                            }
                        })
                    except Exception as e:
                        # 如果音频处理失败，说话人已创建，返回警告
                        return jsonify({
                            "code": 200,
                            "message": f"说话人创建成功，但音频处理失败: {str(e)}",
                            "data": {
                                "speaker_id": speaker_id,
                                "name": name,
                                "description": description,
                                "sample_added": False,
                                "error": str(e)
                            }
                        })

            # 没有音频文件，只创建说话人
            return jsonify({
                "code": 200,
                "message": "创建说话人成功",
                "data": {
                    "speaker_id": speaker_id,
                    "name": name,
                    "description": description,
                    "sample_added": False
                }
            })

        else:
            # 处理JSON请求方式（保持向后兼容）
            data = request.get_json()
            if not data or 'name' not in data:
                return jsonify({
                    "code": 400,
                    "message": "缺少必要参数: name",
                    "data": None
                }), 400

            name = data['name'].strip()
            description = data.get('description', '').strip()

            if not name:
                return jsonify({
                    "code": 400,
                    "message": "说话人姓名不能为空",
                    "data": None
                }), 400

            speaker_id = speaker_service.enroll_speaker(name, description)

            return jsonify({
                "code": 200,
                "message": "创建说话人成功",
                "data": {
                    "speaker_id": speaker_id,
                    "name": name,
                    "description": description,
                    "sample_added": False
                }
            })

    except ValueError as e:
        return jsonify({
            "code": 400,
            "message": str(e),
            "data": None
        }), 400
    except Exception as e:
        print(f"❌ 创建说话人失败: {e}")
        return jsonify({
            "code": 500,
            "message": f"创建说话人失败: {str(e)}",
            "data": None
        }), 500


@app.route('/api/speakers/<speaker_id>', methods=['GET'])
def get_speaker(speaker_id):
    """获取说话人详细信息"""
    try:
        if not speaker_service:
            return jsonify({
                "code": 503,
                "message": "人员识别服务未启用",
                "data": None
            }), 503

        speaker_info = speaker_service.get_speaker_info(speaker_id)
        if not speaker_info:
            return jsonify({
                "code": 404,
                "message": "说话人不存在",
                "data": None
            }), 404

        return jsonify({
            "code": 200,
            "message": "获取说话人信息成功",
            "data": speaker_info
        })

    except Exception as e:
        print(f"❌ 获取说话人信息失败: {e}")
        return jsonify({
            "code": 500,
            "message": f"获取说话人信息失败: {str(e)}",
            "data": None
        }), 500


@app.route('/api/speakers/<speaker_id>', methods=['PUT'])
def update_speaker(speaker_id):
    """更新说话人信息"""
    try:
        if not speaker_service:
            return jsonify({
                "code": 503,
                "message": "人员识别服务未启用",
                "data": None
            }), 503

        data = request.get_json()
        if not data:
            return jsonify({
                "code": 400,
                "message": "缺少更新数据",
                "data": None
            }), 400

        name = data.get('name', '').strip() if 'name' in data else None
        description = data.get('description', '').strip() if 'description' in data else None

        success = speaker_service.db.update_speaker(speaker_id, name, description)
        if not success:
            return jsonify({
                "code": 404,
                "message": "说话人不存在",
                "data": None
            }), 404

        return jsonify({
            "code": 200,
            "message": "更新说话人信息成功",
            "data": None
        })

    except Exception as e:
        print(f"❌ 更新说话人信息失败: {e}")
        return jsonify({
            "code": 500,
            "message": f"更新说话人信息失败: {str(e)}",
            "data": None
        }), 500


@app.route('/api/speakers/<speaker_id>', methods=['DELETE'])
def delete_speaker(speaker_id):
    """删除说话人"""
    try:
        if not speaker_service:
            return jsonify({
                "code": 503,
                "message": "人员识别服务未启用",
                "data": None
            }), 503

        success = speaker_service.delete_speaker(speaker_id)
        if not success:
            return jsonify({
                "code": 404,
                "message": "说话人不存在",
                "data": None
            }), 404

        return jsonify({
            "code": 200,
            "message": "删除说话人成功",
            "data": None
        })

    except Exception as e:
        print(f"❌ 删除说话人失败: {e}")
        return jsonify({
            "code": 500,
            "message": f"删除说话人失败: {str(e)}",
            "data": None
        }), 500


@app.route('/api/speakers/<speaker_id>/samples', methods=['POST'])
def add_audio_sample():
    """为说话人添加音频样本"""
    try:
        if not speaker_service:
            return jsonify({
                "code": 503,
                "message": "人员识别服务未启用",
                "data": None
            }), 503

        speaker_id = request.view_args['speaker_id']

        if 'audio' not in request.files:
            return jsonify({
                "code": 400,
                "message": "没有上传音频文件",
                "data": None
            }), 400

        audio_file = request.files['audio']
        if audio_file.filename == '':
            return jsonify({
                "code": 400,
                "message": "文件名为空",
                "data": None
            }), 400

        # 转换音频格式
        try:
            audio_data, sample_rate = convert_audio_to_wav(audio_file)
        except Exception as e:
            return jsonify({
                "code": 400,
                "message": f"音频格式转换失败: {str(e)}",
                "data": None
            }), 400

        # 获取元数据
        metadata = {
            "original_filename": audio_file.filename,
            "upload_time": datetime.now().isoformat()
        }

        # 添加音频样本
        result = speaker_service.add_audio_sample(
            speaker_id=speaker_id,
            audio_data=audio_data,
            sample_rate=sample_rate,
            metadata=metadata
        )

        return jsonify({
            "code": 200,
            "message": "添加音频样本成功",
            "data": result
        })

    except ValueError as e:
        return jsonify({
            "code": 400,
            "message": str(e),
            "data": None
        }), 400
    except Exception as e:
        print(f"❌ 添加音频样本失败: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({
            "code": 500,
            "message": f"添加音频样本失败: {str(e)}",
            "data": None
        }), 500


@app.route('/api/speakers/<speaker_id>/samples', methods=['GET'])
def get_audio_samples(speaker_id):
    """获取说话人的音频样本列表"""
    try:
        if not speaker_service:
            return jsonify({
                "code": 503,
                "message": "人员识别服务未启用",
                "data": None
            }), 503

        samples = speaker_service.db.get_audio_samples(speaker_id)

        return jsonify({
            "code": 200,
            "message": "获取音频样本列表成功",
            "data": {"samples": samples}
        })

    except Exception as e:
        print(f"❌ 获取音频样本列表失败: {e}")
        return jsonify({
            "code": 500,
            "message": f"获取音频样本列表失败: {str(e)}",
            "data": None
        }), 500


@app.route('/api/samples/<sample_id>', methods=['DELETE'])
def delete_audio_sample(sample_id):
    """删除音频样本"""
    try:
        if not speaker_service:
            return jsonify({
                "code": 503,
                "message": "人员识别服务未启用",
                "data": None
            }), 503

        success = speaker_service.delete_audio_sample(sample_id)
        if not success:
            return jsonify({
                "code": 404,
                "message": "音频样本不存在",
                "data": None
            }), 404

        return jsonify({
            "code": 200,
            "message": "删除音频样本成功",
            "data": None
        })

    except Exception as e:
        print(f"❌ 删除音频样本失败: {e}")
        return jsonify({
            "code": 500,
            "message": f"删除音频样本失败: {str(e)}",
            "data": None
        }), 500


@app.route('/api/speakers/identify', methods=['POST'])
def identify_speaker():
    """识别音频中的说话人"""
    try:
        if not speaker_service:
            return jsonify({
                "code": 503,
                "message": "人员识别服务未启用",
                "data": None
            }), 503

        if 'audio' not in request.files:
            return jsonify({
                "code": 400,
                "message": "没有上传音频文件",
                "data": None
            }), 400

        audio_file = request.files['audio']
        if audio_file.filename == '':
            return jsonify({
                "code": 400,
                "message": "文件名为空",
                "data": None
            }), 400

        # 转换音频格式
        try:
            audio_data, sample_rate = convert_audio_to_wav(audio_file)
        except Exception as e:
            return jsonify({
                "code": 400,
                "message": f"音频格式转换失败: {str(e)}",
                "data": None
            }), 400

        # 进行说话人识别
        result = speaker_service.identify_speaker(audio_data, sample_rate)

        return jsonify({
            "code": 200,
            "message": "说话人识别完成",
            "data": result
        })

    except Exception as e:
        print(f"❌ 说话人识别失败: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({
            "code": 500,
            "message": f"说话人识别失败: {str(e)}",
            "data": None
        }), 500


@app.route('/api/speakers/stats', methods=['GET'])
def get_speaker_stats():
    """获取人员识别系统统计信息"""
    try:
        if not speaker_service:
            return jsonify({
                "code": 503,
                "message": "人员识别服务未启用",
                "data": None
            }), 503

        stats = speaker_service.get_statistics()

        return jsonify({
            "code": 200,
            "message": "获取统计信息成功",
            "data": stats
        })

    except Exception as e:
        print(f"❌ 获取统计信息失败: {e}")
        return jsonify({
            "code": 500,
            "message": f"获取统计信息失败: {str(e)}",
            "data": None
        }), 500


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser()
    parser.add_argument("--port", type=int, default=SERVER_CONFIG['port'])
    parser.add_argument("--model-dir", type=str, default="./models")
    parser.add_argument("--device", type=str, choices=["cuda", "cpu"], default="cpu")
    args = parser.parse_args()

    print("🎙️ 音频转文字服务启动中...")
    print("支持的接口:")
    print("1. POST /transcribe - 离线文件转录")
    print("2. WebSocket /socket.io - 实时转录")
    print("3. WebSocket /socket.io - 离线转录 (支持大文件)")
    print(f"4. 访问 http://localhost:{args.port} 查看测试页面")
    print(f"5. GET /api/sessions - 查看会话状态")
    print("6. 人员管理API:")
    print("   - GET/POST /api/speakers - 说话人管理")
    print("   - GET/PUT/DELETE /api/speakers/<id> - 说话人操作")
    print("   - POST /api/speakers/<id>/samples - 添加音频样本")
    print("   - POST /api/speakers/identify - 识别说话人")
    print("   - GET /api/speakers/stats - 统计信息")
    print(f"模型目录: {args.model_dir}")
    print(f"设备: {args.device}")
    print(f"会话超时: {SESSION_CONFIG['max_idle_time'] / 60}分钟")
    print(f"最大并发: {SESSION_CONFIG['max_concurrent_sessions']}个会话")

    # 初始化服务
    try:
        print("\n🔧 正在初始化离线转录服务...")
        offline_service = OfflineAudioTranscriptionService()
        print("✅ 离线转录服务初始化成功")

        print("\n🔧 正在初始化实时转录服务...")
        realtime_service = RealtimeAudioTranscriptionService()
        print("✅ 实时转录服务初始化成功")

        print("\n🔧 正在初始化异步音频处理器...")
        audio_processor = AudioProcessingManager(socketio, realtime_service, REALTIME_CONFIG)
        print("✅ 异步音频处理器初始化成功")

        print("\n🔧 正在初始化人员识别服务...")
        try:
            global speaker_service
            speaker_service = SpeakerEnrollmentService()
            print("✅ 人员识别服务初始化成功")
        except Exception as e:
            print(f"⚠️ 人员识别服务初始化失败: {e}")
            print("⚠️ 将继续运行，但人员识别功能不可用")
            speaker_service = None

        print("\n🧹 启动会话清理服务...")
        start_session_cleanup()
        print("✅ 会话管理服务启动成功")

        print("\n🚀 所有服务初始化完成，准备启动...")
    except Exception as e:
        print(f"❌ 服务初始化失败: {e}")
        exit(1)

    # 启动服务
    socketio.run(
        app,
        host=SERVER_CONFIG['host'],
        port=args.port,
        debug=SERVER_CONFIG['debug'],
        allow_unsafe_werkzeug=True,
        use_reloader=False
    )