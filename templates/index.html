<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音频转文字服务</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #007bff;
            padding-bottom: 10px;
        }

        .section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }

        .section h2 {
            color: #007bff;
            margin-top: 0;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }

        input[type="file"], select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        input[type="checkbox"] {
            margin-right: 8px;
        }

        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
            margin-bottom: 10px;
            transition: background-color 0.3s;
        }

        button:hover:not(:disabled) {
            background-color: #0056b3;
        }

        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }

        #stopBtn, #cancelOfflineBtn {
            background-color: #dc3545;
        }

        #stopBtn:hover:not(:disabled), #cancelOfflineBtn:hover:not(:disabled) {
            background-color: #c82333;
        }

        #clearBtn {
            background-color: #6c757d;
        }

        #clearBtn:hover:not(:disabled) {
            background-color: #545b62;
        }

        .result {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: white;
            min-height: 100px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            white-space: pre-wrap;
        }

        .success {
            color: #28a745;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 4px;
            margin: 5px 0;
        }

        .error {
            color: #dc3545;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 4px;
            margin: 5px 0;
        }

        .warning {
            color: #856404;
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 4px;
            margin: 5px 0;
        }

        .info {
            color: #0c5460;
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            padding: 10px;
            border-radius: 4px;
            margin: 5px 0;
        }

        .speaker-result {
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 10px;
            margin: 10px 0;
        }

        .segment {
            margin: 8px 0;
            padding: 8px;
            border-radius: 4px;
            background-color: white;
            word-wrap: break-word;
        }

        .segment.speaker-0 {
            border-left: 4px solid #007bff;
        }

        .segment.speaker-1 {
            border-left: 4px solid #28a745;
        }

        .segment.speaker-2 {
            border-left: 4px solid #ffc107;
        }

        .segment.speaker-3 {
            border-left: 4px solid #dc3545;
        }

        .speaker-tag {
            font-weight: bold;
            color: #007bff;
            margin-right: 8px;
        }

        .time-tag {
            color: #6c757d;
            font-size: 0.9em;
            margin-right: 8px;
        }

        .timestamp-tag {
            font-size: 0.8em;
            color: #666;
            margin-right: 5px;
            background-color: #e9ecef;
            padding: 1px 4px;
            border-radius: 3px;
        }

        .progress-container {
            width: 100%;
            background-color: #f0f0f0;
            border-radius: 4px;
            margin: 10px 0;
            height: 20px;
        }

        .progress-fill {
            height: 100%;
            background-color: #007bff;
            border-radius: 4px;
            transition: width 0.3s ease;
            width: 0%;
        }

        .api-info {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .api-info h3 {
            margin-top: 0;
            color: #0056b3;
        }

        .api-info code {
            background-color: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }

        /* 实时转录历史记录样式 */
        .realtime-history {
            max-height: 350px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            background-color: #f9f9f9;
        }

        .simple-segment {
            margin: 5px 0;
            padding: 8px;
            background-color: #e3f2fd;
            border-left: 3px solid #2196f3;
            border-radius: 3px;
            word-wrap: break-word;
        }

        .realtime-stats {
            text-align: center;
            margin-top: 10px;
            color: #666;
            font-style: italic;
        }

        /* 滚动条样式 */
        .realtime-history::-webkit-scrollbar,
        .result::-webkit-scrollbar {
            width: 6px;
        }

        .realtime-history::-webkit-scrollbar-track,
        .result::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .realtime-history::-webkit-scrollbar-thumb,
        .result::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 3px;
        }

        .realtime-history::-webkit-scrollbar-thumb:hover,
        .result::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

        /* 动画效果 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .segment, .simple-segment {
            animation: fadeIn 0.3s ease-out;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎙️ 音频转文字服务</h1>

        <div class="api-info">
            <h3>📋 API 接口信息</h3>
            <p><strong>离线转录:</strong> <code>POST /transcribe</code> - 上传音频文件进行转录</p>
            <p><strong>离线转录 (WebSocket):</strong> <code>WebSocket /socket.io</code> - 支持大文件和进度反馈</p>
            <p><strong>实时转录:</strong> <code>WebSocket /socket.io</code> - 实时语音转文字</p>
            <p><strong>会话状态:</strong> <code>GET /api/sessions</code> - 查看当前会话状态</p>
            <p><strong>人员管理:</strong> <code>GET/POST /api/speakers</code> - 人员音频录入和识别</p>
        </div>

        <!-- 离线转录 -->
        <div class="section">
            <h2>📁 离线转录 (文件上传)</h2>
            <form id="uploadForm" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="audioFile">选择音频文件:</label>
                    <input type="file" id="audioFile" name="audio" accept="audio/*" required>
                </div>

                <div class="form-group">
                    <label>
                        <input type="checkbox" id="enableSpeaker">
                        启用说话人分离
                    </label>
                </div>

                <button type="submit">上传并转录</button>
            </form>

            <div id="uploadResult" class="result"></div>
        </div>

        <!-- 离线转录 (WebSocket方式) -->
        <div class="section">
            <h2>📁 离线转录 (WebSocket)</h2>
            <form id="offlineWebSocketForm">
                <div class="form-group">
                    <label for="offlineAudioFile">选择音频文件:</label>
                    <input type="file" id="offlineAudioFile" accept="audio/*" required>
                </div>

                <div class="form-group">
                    <label>
                        <input type="checkbox" id="enableOfflineSpeaker">
                        启用说话人分离
                    </label>
                </div>

                <button type="button" id="startOfflineBtn" onclick="startOfflineWebSocket()">开始WebSocket转录</button>
                <button type="button" id="cancelOfflineBtn" onclick="cancelOfflineWebSocket()" disabled>取消转录</button>
            </form>

            <!-- 进度条 -->
            <div id="offlineProgress" class="progress-container" style="display: none;">
                <div id="offlineProgressBar" class="progress-fill"></div>
            </div>
            <div id="offlineProgressText" style="display: none;"></div>

            <div id="offlineWebSocketResult" class="result"></div>
        </div>

        <!-- 实时转录 -->
        <div class="section">
            <h2>🎤 实时转录 (WebSocket)</h2>
            <div class="form-group">
                <label>
                    <input type="checkbox" id="enableRealtimeSpeaker">
                    启用说话人分离
                </label>
            </div>

            <button id="startBtn" onclick="startRealtime()">开始实时转录</button>
            <button id="stopBtn" onclick="stopRealtime()" disabled>停止转录</button>
            <button id="clearBtn" onclick="clearRealtimeHistory()" disabled>清空历史</button>

            <div id="realtimeResult" class="result"></div>
        </div>

        <!-- 人员管理 -->
        <div class="section">
            <h2>👥 人员管理</h2>

            <!-- 人员列表 -->
            <div class="form-group">
                <button onclick="loadSpeakers()">刷新人员列表</button>
                <button onclick="showAddSpeakerForm()">添加新人员</button>
                <button onclick="showSpeakerStats()">查看统计</button>
            </div>

            <div id="speakerList" class="result"></div>

            <!-- 添加人员表单 -->
            <div id="addSpeakerForm" style="display: none; margin-top: 20px; padding: 20px; border: 2px solid #007bff; border-radius: 8px; background-color: #f8f9fa;">
                <h3>添加新人员</h3>
                <div class="form-group">
                    <label for="speakerName">姓名:</label>
                    <input type="text" id="speakerName" placeholder="请输入姓名" required>
                </div>
                <div class="form-group">
                    <label for="speakerDescription">描述:</label>
                    <input type="text" id="speakerDescription" placeholder="可选描述信息">
                </div>
                <button onclick="addSpeaker()">确认添加</button>
                <button onclick="hideAddSpeakerForm()">取消</button>
            </div>

            <!-- 音频样本管理 -->
            <div id="sampleManagement" style="display: none; margin-top: 20px; padding: 20px; border: 2px solid #28a745; border-radius: 8px; background-color: #f8fff8;">
                <h3 id="sampleManagementTitle">音频样本管理</h3>

                <!-- 录音功能 -->
                <div class="form-group">
                    <button id="startRecordBtn" onclick="startRecording()">开始录音</button>
                    <button id="stopRecordBtn" onclick="stopRecording()" disabled>停止录音</button>
                    <button id="playRecordBtn" onclick="playRecording()" disabled>播放录音</button>
                    <button id="uploadRecordBtn" onclick="uploadRecording()" disabled>上传样本</button>
                </div>

                <div id="recordingStatus" class="info" style="display: none;"></div>
                <audio id="audioPlayback" controls style="display: none; width: 100%; margin: 10px 0;"></audio>

                <!-- 文件上传 -->
                <div class="form-group">
                    <label for="sampleAudioFile">或选择音频文件:</label>
                    <input type="file" id="sampleAudioFile" accept="audio/*">
                    <button onclick="uploadAudioFile()">上传文件</button>
                </div>

                <!-- 样本列表 -->
                <div id="sampleList" class="result"></div>

                <button onclick="hideSampleManagement()">关闭</button>
            </div>

            <!-- 说话人识别测试 -->
            <div class="form-group" style="margin-top: 20px;">
                <h3>🔍 说话人识别测试</h3>
                <input type="file" id="identifyAudioFile" accept="audio/*">
                <button onclick="identifySpeaker()">识别说话人</button>
            </div>

            <div id="identifyResult" class="result"></div>
        </div>

        <!-- 会话状态 -->
        <div class="section">
            <h2>📊 会话状态</h2>
            <button onclick="checkSessions()">查看会话状态</button>
            <div id="sessionResult" class="result"></div>
        </div>
    </div>

    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <script src="/static/js/realtime.js"></script>
    <script src="/static/js/offline-websocket.js"></script>
    <script src="/static/js/speaker-management.js"></script>
    <script>
        // 人员管理相关变量
        let mediaRecorder = null;
        let recordedChunks = [];
        let currentSpeakerId = null;
        let recordedBlob = null;
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📄 页面加载完成');

            // 检查Socket.IO是否加载
            if (typeof io === 'undefined') {
                console.error('❌ Socket.IO未加载');
            } else {
                console.log('✅ Socket.IO已加载，版本:', io.version || 'unknown');
            }

            // 检查必要的DOM元素
            const elements = [
                'offlineAudioFile',
                'enableOfflineSpeaker',
                'startOfflineBtn',
                'cancelOfflineBtn',
                'offlineWebSocketResult',
                'offlineProgress',
                'offlineProgressBar',
                'offlineProgressText',
                'enableRealtimeSpeaker',
                'startBtn',
                'stopBtn',
                'clearBtn',
                'realtimeResult'
            ];

            elements.forEach(id => {
                const element = document.getElementById(id);
                if (!element) {
                    console.error(`❌ 缺少元素: ${id}`);
                } else {
                    console.log(`✅ 找到元素: ${id}`);
                }
            });
        });

        // 离线转录表单提交
        document.getElementById('uploadForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData();
            const audioFile = document.getElementById('audioFile').files[0];
            const enableSpeaker = document.getElementById('enableSpeaker').checked;

            if (!audioFile) {
                document.getElementById('uploadResult').innerHTML = '<div class="error">请选择音频文件</div>';
                return;
            }

            formData.append('audio', audioFile);
            formData.append('enable_speaker', enableSpeaker);

            const resultDiv = document.getElementById('uploadResult');
            resultDiv.innerHTML = '<div class="info">🔄 正在处理音频文件，请稍候...</div>';

            try {
                const response = await fetch('/transcribe', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.code === 200) {
                    let html = '<div class="success">✅ 转录成功</div>';

                    if (result.data.segmented_text && result.data.segmented_text.length > 0) {
                        html += '<div class="speaker-result">';
                        html += '<h3>🎭 说话人分离结果:</h3>';

                        result.data.segmented_text.forEach(segment => {
                            const speakerClass = segment.speaker.replace('_', '-').toLowerCase();
                            html += `<div class="segment ${speakerClass}">
                                <span class="speaker-tag">${segment.speaker}</span>
                                <span class="time-tag">(${segment.start_time.toFixed(1)}s-${segment.end_time.toFixed(1)}s)</span>:
                                ${segment.text}
                            </div>`;
                        });
                        html += '</div>';
                    } else {
                        html += `<p><strong>📝 转录结果:</strong></p><p>${result.data.voice_text}</p>`;
                    }

                    if (result.data.process_time) {
                        html += `<p><strong>⏱️ 处理时间:</strong> ${result.data.process_time}</p>`;
                    }

                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ 转录失败: ${result.message}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 请求失败: ${error.message}</div>`;
            }
        });

        // 查看会话状态
        async function checkSessions() {
            const resultDiv = document.getElementById('sessionResult');
            resultDiv.innerHTML = '<div class="info">🔄 正在获取会话状态...</div>';

            try {
                const response = await fetch('/api/sessions');
                const result = await response.json();

                if (result.code === 200) {
                    let html = '<div class="success">✅ 会话状态获取成功</div>';
                    html += `<p><strong>📊 活跃会话数:</strong> ${result.data.active_sessions}</p>`;
                    html += `<p><strong>📈 最大并发数:</strong> ${result.data.max_concurrent_sessions}</p>`;
                    html += `<p><strong>⏰ 会话超时时间:</strong> ${Math.floor(result.data.max_idle_time / 60)}分钟</p>`;

                    if (result.data.sessions && result.data.sessions.length > 0) {
                        html += '<h4>🔗 当前会话列表:</h4>';
                        result.data.sessions.forEach(session => {
                            const lastActivity = new Date(session.last_activity).toLocaleString();
                            html += `<div class="info">
                                <strong>会话ID:</strong> ${session.session_id}<br>
                                <strong>类型:</strong> ${session.type}<br>
                                <strong>最后活动:</strong> ${lastActivity}<br>
                                <strong>说话人分离:</strong> ${session.enable_speaker ? '启用' : '禁用'}`;

                            if (session.type === 'realtime') {
                                html += `<br><strong>音频块数:</strong> ${session.audio_chunks_received}`;
                                html += `<br><strong>音频时长:</strong> ${session.total_audio_duration.toFixed(1)}秒`;
                            } else if (session.type === 'offline') {
                                html += `<br><strong>文件名:</strong> ${session.filename}`;
                                html += `<br><strong>上传进度:</strong> ${((session.total_size / session.expected_size) * 100).toFixed(1)}%`;
                            }

                            html += '</div>';
                        });
                    }

                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ 获取失败: ${result.message}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 请求失败: ${error.message}</div>`;
            }
        }

        // ==================== 人员管理功能 ====================

        // 加载说话人列表
        async function loadSpeakers() {
            const resultDiv = document.getElementById('speakerList');
            resultDiv.innerHTML = '<div class="info">🔄 正在加载人员列表...</div>';

            try {
                const response = await fetch('/api/speakers');
                const result = await response.json();

                if (result.code === 200) {
                    let html = '<div class="success">✅ 人员列表加载成功</div>';

                    if (result.data.speakers && result.data.speakers.length > 0) {
                        html += '<div style="margin-top: 15px;">';
                        result.data.speakers.forEach(speaker => {
                            html += `
                                <div class="speaker-item" style="border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 8px; background-color: white;">
                                    <div style="display: flex; justify-content: space-between; align-items: center;">
                                        <div>
                                            <strong>👤 ${speaker.name}</strong>
                                            ${speaker.description ? `<br><small>${speaker.description}</small>` : ''}
                                            <br><small>样本数: ${speaker.sample_count} | 创建时间: ${new Date(speaker.created_at).toLocaleString()}</small>
                                        </div>
                                        <div>
                                            <button onclick="manageSamples('${speaker.speaker_id}', '${speaker.name}')" style="margin-right: 5px;">管理样本</button>
                                            <button onclick="deleteSpeaker('${speaker.speaker_id}', '${speaker.name}')" style="background-color: #dc3545;">删除</button>
                                        </div>
                                    </div>
                                </div>
                            `;
                        });
                        html += '</div>';
                    } else {
                        html += '<p>暂无注册人员</p>';
                    }

                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ 加载失败: ${result.message}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 请求失败: ${error.message}</div>`;
            }
        }

        // 显示添加人员表单
        function showAddSpeakerForm() {
            document.getElementById('addSpeakerForm').style.display = 'block';
            document.getElementById('speakerName').focus();
        }

        // 隐藏添加人员表单
        function hideAddSpeakerForm() {
            document.getElementById('addSpeakerForm').style.display = 'none';
            document.getElementById('speakerName').value = '';
            document.getElementById('speakerDescription').value = '';
        }

        // 添加说话人
        async function addSpeaker() {
            const name = document.getElementById('speakerName').value.trim();
            const description = document.getElementById('speakerDescription').value.trim();

            if (!name) {
                alert('请输入姓名');
                return;
            }

            try {
                const response = await fetch('/api/speakers', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        name: name,
                        description: description
                    })
                });

                const result = await response.json();

                if (result.code === 200) {
                    alert(`✅ 添加成功: ${name}`);
                    hideAddSpeakerForm();
                    loadSpeakers(); // 刷新列表
                } else {
                    alert(`❌ 添加失败: ${result.message}`);
                }
            } catch (error) {
                alert(`❌ 请求失败: ${error.message}`);
            }
        }

        // 删除说话人
        async function deleteSpeaker(speakerId, speakerName) {
            if (!confirm(`确定要删除 "${speakerName}" 吗？这将删除所有相关的音频样本。`)) {
                return;
            }

            try {
                const response = await fetch(`/api/speakers/${speakerId}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (result.code === 200) {
                    alert(`✅ 删除成功: ${speakerName}`);
                    loadSpeakers(); // 刷新列表
                } else {
                    alert(`❌ 删除失败: ${result.message}`);
                }
            } catch (error) {
                alert(`❌ 请求失败: ${error.message}`);
            }
        }

        // 管理音频样本
        function manageSamples(speakerId, speakerName) {
            currentSpeakerId = speakerId;
            document.getElementById('sampleManagementTitle').textContent = `${speakerName} - 音频样本管理`;
            document.getElementById('sampleManagement').style.display = 'block';
            loadSamples(speakerId);
        }

        // 隐藏样本管理
        function hideSampleManagement() {
            document.getElementById('sampleManagement').style.display = 'none';
            currentSpeakerId = null;

            // 重置录音状态
            if (mediaRecorder && mediaRecorder.state !== 'inactive') {
                mediaRecorder.stop();
            }
            resetRecordingUI();
        }

        // 加载音频样本列表
        async function loadSamples(speakerId) {
            const resultDiv = document.getElementById('sampleList');
            resultDiv.innerHTML = '<div class="info">🔄 正在加载样本列表...</div>';

            try {
                const response = await fetch(`/api/speakers/${speakerId}/samples`);
                const result = await response.json();

                if (result.code === 200) {
                    let html = '<h4>📁 音频样本列表</h4>';

                    if (result.data.samples && result.data.samples.length > 0) {
                        result.data.samples.forEach(sample => {
                            html += `
                                <div class="sample-item" style="border: 1px solid #ddd; padding: 10px; margin: 5px 0; border-radius: 5px; background-color: #f9f9f9;">
                                    <div style="display: flex; justify-content: space-between; align-items: center;">
                                        <div>
                                            <strong>🎵 样本 ${sample.sample_id.slice(-8)}</strong>
                                            <br><small>时长: ${sample.duration.toFixed(1)}s | 质量: ${(sample.quality_score * 100).toFixed(1)}% | 上传时间: ${new Date(sample.created_at).toLocaleString()}</small>
                                        </div>
                                        <button onclick="deleteSample('${sample.sample_id}')" style="background-color: #dc3545;">删除</button>
                                    </div>
                                </div>
                            `;
                        });
                    } else {
                        html += '<p>暂无音频样本，请录制或上传音频样本</p>';
                    }

                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ 加载失败: ${result.message}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 请求失败: ${error.message}</div>`;
            }
        }

        // 删除音频样本
        async function deleteSample(sampleId) {
            if (!confirm('确定要删除这个音频样本吗？')) {
                return;
            }

            try {
                const response = await fetch(`/api/samples/${sampleId}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (result.code === 200) {
                    alert('✅ 删除成功');
                    loadSamples(currentSpeakerId); // 刷新样本列表
                } else {
                    alert(`❌ 删除失败: ${result.message}`);
                }
            } catch (error) {
                alert(`❌ 请求失败: ${error.message}`);
            }
        }
    </script>
</body>
</html>