#!/usr/bin/env python3
"""
测试说话人识别功能优化
"""
import sys
import os
import json
import requests
import numpy as np
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.speaker_enrollment import SpeakerEnrollmentService
from services.speaker_database import SpeakerDatabase
from services.offline_transcription import OfflineTranscriptionService

def test_speaker_database():
    """测试说话人数据库功能"""
    print("🔧 测试说话人数据库功能...")
    
    try:
        db = SpeakerDatabase()
        
        # 测试添加说话人
        speaker_id = db.add_speaker("测试用户", "测试描述")
        print(f"✅ 添加说话人成功: {speaker_id}")
        
        # 测试获取说话人
        speaker = db.get_speaker(speaker_id)
        print(f"✅ 获取说话人成功: {speaker}")
        
        # 测试列出说话人
        speakers = db.list_speakers()
        print(f"✅ 列出说话人成功: {len(speakers)} 个")
        
        return True
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        return False

def test_speaker_enrollment():
    """测试说话人注册服务"""
    print("🔧 测试说话人注册服务...")
    
    try:
        service = SpeakerEnrollmentService()
        
        # 测试注册说话人
        speaker_id = service.enroll_speaker("测试用户2", "测试描述2")
        print(f"✅ 注册说话人成功: {speaker_id}")
        
        # 测试获取说话人信息
        info = service.get_speaker_info(speaker_id)
        print(f"✅ 获取说话人信息成功: {info}")
        
        # 测试列出说话人
        speakers = service.list_speakers()
        print(f"✅ 列出说话人成功: {len(speakers)} 个")
        
        return True
    except Exception as e:
        print(f"❌ 注册服务测试失败: {e}")
        return False

def test_api_endpoints():
    """测试API接口"""
    print("🔧 测试API接口...")
    
    base_url = "http://localhost:5001"
    
    try:
        # 测试获取说话人列表
        response = requests.get(f"{base_url}/api/speakers")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 获取说话人列表成功: {data.get('code')}")
        else:
            print(f"⚠️ 获取说话人列表失败: {response.status_code}")
        
        # 测试获取可用说话人列表
        response = requests.get(f"{base_url}/api/speakers/available")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 获取可用说话人列表成功: {data.get('code')}")
        else:
            print(f"⚠️ 获取可用说话人列表失败: {response.status_code}")
        
        return True
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def test_targeted_identification():
    """测试定向说话人识别"""
    print("🔧 测试定向说话人识别...")
    
    try:
        service = SpeakerEnrollmentService()
        
        # 创建测试音频数据
        test_audio = np.random.randn(16000).astype(np.float32)  # 1秒的随机音频
        sample_rate = 16000
        
        # 测试定向识别（空列表）
        result = service.identify_targeted_speaker(test_audio, sample_rate, [])
        print(f"✅ 定向识别测试（空列表）: {result['reason']}")
        
        return True
    except Exception as e:
        print(f"❌ 定向识别测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试说话人识别功能优化...")
    
    tests = [
        ("数据库功能", test_speaker_database),
        ("注册服务", test_speaker_enrollment),
        ("定向识别", test_targeted_identification),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n{'='*50}")
    print("测试总结")
    print('='*50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
        return 0
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
        return 1

if __name__ == "__main__":
    sys.exit(main())
