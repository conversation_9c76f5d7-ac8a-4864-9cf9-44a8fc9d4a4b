"""
配置文件
"""

# 模型配置
MODEL_CONFIG = {
    "asr_model_path": "./models/speech_seaco_paraformer_large_asr_nat-zh-cn-16k-common-vocab8404-pytorch",
    "vad_model_path": "./models/speech_fsmn_vad_zh-cn-16k-common-pytorch",
    "punc_model_path": "./models/punc_ct-transformer_zh-cn-common-vocab272727-pytorch",
    "speaker_model_path": "./models/speech_eres2net_large_sv_zh-cn_3dspeaker_16k",
    "device": "cpu"
}

# 离线转录配置
OFFLINE_CONFIG = {
    "enable_vad": True,
    "enable_punc": True,
    "batch_size_s": 300,
    "sample_rate": 16000,
    "use_itn": True
}

# 实时转录配置
REALTIME_CONFIG = {
    "enable_vad": False,            # 关键：实时转录禁用VAD
    "enable_punc": False,           # 实时转录也可以禁用标点，提高速度
    "batch_size_s": 60,             # 批处理大小（批处理大小越小，内存占用越低）
    "sample_rate": 16000,           # 采样率（采样率越低，计算量越少）
    "use_itn": False,               # 禁用逆文本规范化，提升速度
    "process_interval": 5.0,        # 处理间隔，（处理间隔越大，处理频率越小）
    "min_audio_duration": 2.0,      # 最小音频长度
    "context_duration": 2.0,        # 上下文保留时间
    "max_buffer_duration": 10.0,    # 最大缓冲时长
    "overlap_duration": 1.0         # 重叠时间，避免截断
}

# 服务配置
SERVER_CONFIG = {
    "host": "0.0.0.0",
    "port": 5001,
    "debug": True,
    "max_content_length": 100 * 1024 * 1024,
    "secret_key": "audio_transcription_secret"
}

# 会话管理配置
SESSION_CONFIG = {
    "max_idle_time": 1800,          # 30分钟无活动自动关闭 (秒)
    "cleanup_interval": 300,        # 5分钟检查一次过期会话 (秒)
    "heartbeat_interval": 60,       # 1分钟心跳检测 (秒)
    "max_concurrent_sessions": 10   # 最大并发会话数
}

# 人员音频录入配置
SPEAKER_CONFIG = {
    "database_path": "./data/speakers.db",          # 数据库文件路径
    "audio_samples_dir": "./data/audio_samples",    # 音频样本存储目录
    "min_sample_duration": 3.0,                     # 最小样本时长(秒)
    "max_sample_duration": 30.0,                    # 最大样本时长(秒)
    "min_samples_per_person": 2,                    # 每人最少样本数
    "max_samples_per_person": 10,                   # 每人最多样本数
    "similarity_threshold": 0.262,                  # 说话人相似度阈值
    "feature_dim": 512,                             # 特征向量维度
    "sample_rate": 16000,                           # 音频采样率
    "enable_voice_activity_detection": True,        # 启用语音活动检测
    "min_voice_activity_ratio": 0.6                 # 最小语音活动比例
}