#!/usr/bin/env python3
"""
测试修复后的功能
"""
import sys
import os
import json
import requests
import time
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_api_endpoints():
    """测试API接口"""
    print("🔧 测试API接口...")
    
    base_url = "http://localhost:5001"
    
    try:
        # 测试获取说话人列表
        print("测试获取说话人列表...")
        response = requests.get(f"{base_url}/api/speakers", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 获取说话人列表成功: {data.get('code')}")
            print(f"   说话人数量: {len(data.get('data', {}).get('speakers', []))}")
        else:
            print(f"❌ 获取说话人列表失败: {response.status_code}")
            print(f"   响应: {response.text}")
        
        # 测试获取可用说话人列表
        print("测试获取可用说话人列表...")
        response = requests.get(f"{base_url}/api/speakers/available", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 获取可用说话人列表成功: {data.get('code')}")
            print(f"   可用说话人数量: {data.get('data', {}).get('count', 0)}")
        else:
            print(f"❌ 获取可用说话人列表失败: {response.status_code}")
            print(f"   响应: {response.text}")
        
        # 测试获取统计信息
        print("测试获取统计信息...")
        response = requests.get(f"{base_url}/api/speakers/stats", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 获取统计信息成功: {data.get('code')}")
            stats = data.get('data', {})
            print(f"   总人员数: {stats.get('total_speakers', 0)}")
            print(f"   活跃人员数: {stats.get('active_speakers', 0)}")
            print(f"   总样本数: {stats.get('total_samples', 0)}")
            print(f"   总特征数: {stats.get('total_features', 0)}")
        else:
            print(f"❌ 获取统计信息失败: {response.status_code}")
            print(f"   响应: {response.text}")
        
        # 测试添加说话人（JSON方式）
        print("测试添加说话人（JSON方式）...")
        test_data = {
            "name": "测试用户_" + str(int(time.time())),
            "description": "API测试用户"
        }
        response = requests.post(f"{base_url}/api/speakers", 
                               json=test_data, 
                               headers={'Content-Type': 'application/json'},
                               timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 添加说话人成功: {data.get('code')}")
            print(f"   说话人ID: {data.get('data', {}).get('speaker_id')}")
        else:
            print(f"❌ 添加说话人失败: {response.status_code}")
            print(f"   响应: {response.text}")
        
        return True
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保服务器正在运行")
        return False
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def test_database_initialization():
    """测试数据库初始化"""
    print("🔧 测试数据库初始化...")
    
    try:
        from services.speaker_database import SpeakerDatabase
        
        db = SpeakerDatabase()
        
        # 测试获取统计信息
        stats = db.get_database_stats()
        print(f"✅ 数据库统计信息获取成功:")
        print(f"   总人员数: {stats['total_speakers']}")
        print(f"   活跃人员数: {stats['active_speakers']}")
        print(f"   总样本数: {stats['total_samples']}")
        print(f"   总特征数: {stats['total_features']}")
        print(f"   数据库路径: {stats['database_path']}")
        
        return True
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        return False

def test_speaker_service_initialization():
    """测试说话人服务初始化"""
    print("🔧 测试说话人服务初始化...")
    
    try:
        from services.speaker_enrollment import SpeakerEnrollmentService
        
        service = SpeakerEnrollmentService()
        
        # 测试获取统计信息
        stats = service.get_statistics()
        print(f"✅ 说话人服务统计信息获取成功:")
        print(f"   总人员数: {stats['total_speakers']}")
        print(f"   活跃人员数: {stats['active_speakers']}")
        
        # 测试列出说话人
        speakers = service.list_speakers()
        print(f"✅ 列出说话人成功: {len(speakers)} 个")
        
        return True
    except Exception as e:
        print(f"❌ 说话人服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试修复后的功能...")
    
    tests = [
        ("数据库初始化", test_database_initialization),
        ("说话人服务初始化", test_speaker_service_initialization),
        ("API接口", test_api_endpoints),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n{'='*50}")
    print("测试总结")
    print('='*50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
        return 0
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
        return 1

if __name__ == "__main__":
    sys.exit(main())
