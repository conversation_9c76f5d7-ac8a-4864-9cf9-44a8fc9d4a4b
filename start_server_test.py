#!/usr/bin/env python3
"""
启动服务器进行测试
"""
import sys
import os
import subprocess
import time
import signal
import threading
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def start_server():
    """启动服务器"""
    print("🚀 启动服务器...")
    
    try:
        # 启动服务器进程
        process = subprocess.Popen([
            sys.executable, "app.py", 
            "--port", "5001", 
            "--device", "cpu"
        ], stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True)
        
        print("⏳ 等待服务器启动...")
        
        # 等待服务器启动
        startup_timeout = 30
        for i in range(startup_timeout):
            if process.poll() is not None:
                # 进程已退出
                output, _ = process.communicate()
                print(f"❌ 服务器启动失败:")
                print(output)
                return None
            
            time.sleep(1)
            print(f"   等待中... ({i+1}/{startup_timeout})")
            
            # 检查是否有启动成功的标志
            try:
                import requests
                response = requests.get("http://localhost:5001", timeout=1)
                if response.status_code == 200:
                    print("✅ 服务器启动成功!")
                    return process
            except:
                continue
        
        print("⚠️ 服务器启动超时，但进程仍在运行")
        return process
        
    except Exception as e:
        print(f"❌ 启动服务器失败: {e}")
        return None

def run_tests():
    """运行测试"""
    print("\n🧪 运行测试...")
    
    try:
        result = subprocess.run([sys.executable, "test_fixes.py"], 
                              capture_output=True, text=True, timeout=60)
        
        print("测试输出:")
        print(result.stdout)
        
        if result.stderr:
            print("测试错误:")
            print(result.stderr)
        
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        print("❌ 测试超时")
        return False
    except Exception as e:
        print(f"❌ 运行测试失败: {e}")
        return False

def stop_server(process):
    """停止服务器"""
    if process:
        print("\n🛑 停止服务器...")
        try:
            process.terminate()
            process.wait(timeout=5)
            print("✅ 服务器已停止")
        except subprocess.TimeoutExpired:
            print("⚠️ 强制终止服务器...")
            process.kill()
            process.wait()
            print("✅ 服务器已强制终止")

def main():
    """主函数"""
    print("🔧 开始服务器测试...")
    
    server_process = None
    
    try:
        # 启动服务器
        server_process = start_server()
        if not server_process:
            print("❌ 无法启动服务器，测试终止")
            return 1
        
        # 等待一段时间确保服务器完全启动
        time.sleep(5)
        
        # 运行测试
        test_success = run_tests()
        
        if test_success:
            print("\n🎉 所有测试通过!")
            return 0
        else:
            print("\n⚠️ 部分测试失败")
            return 1
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
        return 1
    except Exception as e:
        print(f"\n❌ 测试过程出错: {e}")
        return 1
    finally:
        # 确保服务器被停止
        stop_server(server_process)

if __name__ == "__main__":
    sys.exit(main())
