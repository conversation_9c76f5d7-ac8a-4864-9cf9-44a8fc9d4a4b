"""
人员音频录入服务
处理音频样本录制、特征提取、质量评估等功能
"""
import os
import numpy as np
import soundfile as sf
import tempfile
import subprocess
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from datetime import datetime

from config import SPEAKER_CONFIG, MODEL_CONFIG
from services.speaker_database import SpeakerDatabase


class SpeakerEnrollmentService:
    """人员音频录入服务"""
    
    def __init__(self):
        print("🔧 初始化人员音频录入服务...")
        self.db = SpeakerDatabase()
        self.speaker_model = None
        self.vad_model = None
        self._load_models()
        print("✅ 人员音频录入服务初始化完成")
    
    def _load_models(self):
        """加载说话人识别和VAD模型"""
        try:
            print("正在加载说话人识别模型...")
            from modelscope.pipelines import pipeline
            from modelscope.utils.constant import Tasks
            
            # 加载说话人识别模型
            self.speaker_model = pipeline(
                task=Tasks.speaker_verification,
                model=MODEL_CONFIG["speaker_model_path"],
                device=MODEL_CONFIG["device"]
            )
            print("✅ 说话人识别模型加载成功")
            
            # 加载VAD模型（用于语音活动检测）
            if SPEAKER_CONFIG["enable_voice_activity_detection"]:
                from funasr import AutoModel
                self.vad_model = AutoModel(
                    model=MODEL_CONFIG["vad_model_path"],
                    device=MODEL_CONFIG["device"],
                    disable_update=True
                )
                print("✅ VAD模型加载成功")
                
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            raise e
    
    def enroll_speaker(self, name: str, description: str = "") -> str:
        """注册新的说话人"""
        try:
            speaker_id = self.db.add_speaker(name, description)
            print(f"✅ 说话人注册成功: {name} (ID: {speaker_id})")
            return speaker_id
        except Exception as e:
            print(f"❌ 说话人注册失败: {e}")
            raise e
    
    def add_audio_sample(self, speaker_id: str, audio_data: np.ndarray, 
                        sample_rate: int, metadata: Dict = None) -> Dict:
        """添加音频样本"""
        try:
            # 验证说话人存在
            speaker = self.db.get_speaker(speaker_id)
            if not speaker:
                raise ValueError(f"说话人不存在: {speaker_id}")
            
            # 检查样本数量限制
            current_samples = self.db.get_audio_samples(speaker_id)
            if len(current_samples) >= SPEAKER_CONFIG["max_samples_per_person"]:
                raise ValueError(f"已达到最大样本数限制: {SPEAKER_CONFIG['max_samples_per_person']}")
            
            # 预处理音频
            processed_audio, processed_sr = self._preprocess_audio(audio_data, sample_rate)
            
            # 质量评估
            quality_result = self._assess_audio_quality(processed_audio, processed_sr)
            
            if not quality_result["is_valid"]:
                raise ValueError(f"音频质量不符合要求: {quality_result['reason']}")
            
            # 保存音频文件
            audio_file_path = self._save_audio_file(speaker_id, processed_audio, processed_sr)
            
            # 提取特征向量
            feature_vector = self._extract_speaker_features(processed_audio, processed_sr)
            
            # 添加到数据库
            sample_id = self.db.add_audio_sample(
                speaker_id=speaker_id,
                file_path=audio_file_path,
                duration=len(processed_audio) / processed_sr,
                sample_rate=processed_sr,
                quality_score=quality_result["quality_score"],
                voice_activity_ratio=quality_result["voice_activity_ratio"],
                metadata=metadata
            )
            
            # 保存特征向量
            feature_id = self.db.add_speaker_feature(
                speaker_id=speaker_id,
                sample_id=sample_id,
                feature_vector=feature_vector
            )
            
            result = {
                "sample_id": sample_id,
                "feature_id": feature_id,
                "file_path": audio_file_path,
                "duration": len(processed_audio) / processed_sr,
                "quality_score": quality_result["quality_score"],
                "voice_activity_ratio": quality_result["voice_activity_ratio"],
                "feature_dim": len(feature_vector)
            }
            
            print(f"✅ 音频样本添加成功: {sample_id}")
            return result
            
        except Exception as e:
            print(f"❌ 添加音频样本失败: {e}")
            raise e
    
    def _preprocess_audio(self, audio_data: np.ndarray, sample_rate: int) -> Tuple[np.ndarray, int]:
        """预处理音频数据"""
        # 确保音频是单声道
        if len(audio_data.shape) > 1:
            audio_data = np.mean(audio_data, axis=1)
        
        # 重采样到目标采样率
        target_sr = SPEAKER_CONFIG["sample_rate"]
        if sample_rate != target_sr:
            import librosa
            audio_data = librosa.resample(
                audio_data, 
                orig_sr=sample_rate, 
                target_sr=target_sr
            )
        
        # 音频归一化
        if np.max(np.abs(audio_data)) > 0:
            audio_data = audio_data / np.max(np.abs(audio_data)) * 0.9
        
        # 确保数据类型
        audio_data = np.ascontiguousarray(audio_data, dtype=np.float32)
        
        return audio_data, target_sr
    
    def _assess_audio_quality(self, audio_data: np.ndarray, sample_rate: int) -> Dict:
        """评估音频质量"""
        duration = len(audio_data) / sample_rate
        
        # 检查时长
        if duration < SPEAKER_CONFIG["min_sample_duration"]:
            return {
                "is_valid": False,
                "reason": f"音频时长太短: {duration:.1f}s < {SPEAKER_CONFIG['min_sample_duration']}s",
                "quality_score": 0.0,
                "voice_activity_ratio": 0.0
            }
        
        if duration > SPEAKER_CONFIG["max_sample_duration"]:
            return {
                "is_valid": False,
                "reason": f"音频时长太长: {duration:.1f}s > {SPEAKER_CONFIG['max_sample_duration']}s",
                "quality_score": 0.0,
                "voice_activity_ratio": 0.0
            }
        
        # 语音活动检测
        voice_activity_ratio = 1.0  # 默认值
        
        if self.vad_model and SPEAKER_CONFIG["enable_voice_activity_detection"]:
            try:
                vad_result = self.vad_model.generate(input=audio_data)
                if isinstance(vad_result, list) and len(vad_result) > 0:
                    # 计算语音活动比例
                    total_frames = len(audio_data)
                    voice_frames = 0
                    
                    for segment in vad_result[0].get('value', []):
                        start_frame = int(segment[0] * sample_rate / 1000)
                        end_frame = int(segment[1] * sample_rate / 1000)
                        voice_frames += max(0, min(end_frame, total_frames) - max(start_frame, 0))
                    
                    voice_activity_ratio = voice_frames / total_frames if total_frames > 0 else 0
                    
            except Exception as e:
                print(f"⚠️ VAD检测失败: {e}")
                voice_activity_ratio = 0.8  # 假设有合理的语音活动
        
        # 检查语音活动比例
        min_ratio = SPEAKER_CONFIG["min_voice_activity_ratio"]
        if voice_activity_ratio < min_ratio:
            return {
                "is_valid": False,
                "reason": f"语音活动比例太低: {voice_activity_ratio:.2f} < {min_ratio}",
                "quality_score": voice_activity_ratio,
                "voice_activity_ratio": voice_activity_ratio
            }
        
        # 计算质量分数（基于信噪比、语音活动比例等）
        quality_score = self._calculate_quality_score(audio_data, voice_activity_ratio)
        
        return {
            "is_valid": True,
            "reason": "音频质量符合要求",
            "quality_score": quality_score,
            "voice_activity_ratio": voice_activity_ratio
        }
    
    def _calculate_quality_score(self, audio_data: np.ndarray, voice_activity_ratio: float) -> float:
        """计算音频质量分数"""
        # 简单的质量评估算法
        # 基于信号强度、语音活动比例等因素
        
        # 信号强度评分
        rms = np.sqrt(np.mean(audio_data ** 2))
        signal_score = min(1.0, rms * 10)  # 假设合理的RMS范围
        
        # 语音活动比例评分
        activity_score = voice_activity_ratio
        
        # 动态范围评分
        dynamic_range = np.max(audio_data) - np.min(audio_data)
        dynamic_score = min(1.0, dynamic_range / 2.0)
        
        # 综合评分
        quality_score = (signal_score * 0.4 + activity_score * 0.4 + dynamic_score * 0.2)
        
        return min(1.0, max(0.0, quality_score))
    
    def _save_audio_file(self, speaker_id: str, audio_data: np.ndarray, sample_rate: int) -> str:
        """保存音频文件"""
        # 创建说话人目录
        speaker_dir = Path(SPEAKER_CONFIG["audio_samples_dir"]) / speaker_id
        speaker_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"sample_{timestamp}.wav"
        file_path = speaker_dir / filename
        
        # 保存音频文件
        sf.write(str(file_path), audio_data, sample_rate)
        
        return str(file_path)
    
    def _extract_speaker_features(self, audio_data: np.ndarray, sample_rate: int) -> np.ndarray:
        """提取说话人特征向量"""
        try:
            # 使用说话人识别模型提取特征
            # 注意：这里需要根据实际模型API调整
            
            # 创建临时文件
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                sf.write(temp_file.name, audio_data, sample_rate)
                temp_path = temp_file.name
            
            try:
                # 使用模型提取特征
                # 这里假设模型有提取embedding的方法
                # 实际实现可能需要根据具体模型API调整
                result = self.speaker_model([temp_path])
                
                # 提取特征向量（这里需要根据实际返回格式调整）
                if isinstance(result, dict) and 'embedding' in result:
                    feature_vector = np.array(result['embedding'])
                elif isinstance(result, dict) and 'output_embedding' in result:
                    feature_vector = np.array(result['output_embedding'])
                else:
                    # 如果没有直接的embedding输出，使用一个简单的特征提取方法
                    feature_vector = self._extract_simple_features(audio_data, sample_rate)
                
                return feature_vector.astype(np.float32)
                
            finally:
                # 清理临时文件
                if os.path.exists(temp_path):
                    os.remove(temp_path)
                    
        except Exception as e:
            print(f"⚠️ 特征提取失败，使用简单特征: {e}")
            return self._extract_simple_features(audio_data, sample_rate)
    
    def _extract_simple_features(self, audio_data: np.ndarray, sample_rate: int) -> np.ndarray:
        """提取简单的音频特征（备用方案）"""
        try:
            import librosa
            
            # 提取MFCC特征
            mfccs = librosa.feature.mfcc(y=audio_data, sr=sample_rate, n_mfcc=13)
            mfcc_mean = np.mean(mfccs, axis=1)
            mfcc_std = np.std(mfccs, axis=1)
            
            # 提取谱质心
            spectral_centroids = librosa.feature.spectral_centroid(y=audio_data, sr=sample_rate)
            centroid_mean = np.mean(spectral_centroids)
            centroid_std = np.std(spectral_centroids)
            
            # 提取零交叉率
            zcr = librosa.feature.zero_crossing_rate(audio_data)
            zcr_mean = np.mean(zcr)
            zcr_std = np.std(zcr)
            
            # 组合特征
            features = np.concatenate([
                mfcc_mean, mfcc_std, 
                [centroid_mean, centroid_std, zcr_mean, zcr_std]
            ])
            
            # 填充到目标维度
            target_dim = SPEAKER_CONFIG["feature_dim"]
            if len(features) < target_dim:
                features = np.pad(features, (0, target_dim - len(features)), 'constant')
            elif len(features) > target_dim:
                features = features[:target_dim]
            
            return features.astype(np.float32)
            
        except Exception as e:
            print(f"⚠️ 简单特征提取也失败: {e}")
            # 返回随机特征向量作为最后的备用方案
            return np.random.randn(SPEAKER_CONFIG["feature_dim"]).astype(np.float32)
    
    def identify_speaker(self, audio_data: np.ndarray, sample_rate: int, 
                        threshold: float = None) -> Dict:
        """识别说话人"""
        try:
            threshold = threshold or SPEAKER_CONFIG["similarity_threshold"]
            
            # 预处理音频
            processed_audio, processed_sr = self._preprocess_audio(audio_data, sample_rate)
            
            # 提取特征
            query_features = self._extract_speaker_features(processed_audio, processed_sr)
            
            # 获取所有注册的说话人特征
            all_speaker_features = self.db.get_all_speaker_features()
            
            if not all_speaker_features:
                return {
                    "identified": False,
                    "speaker_id": None,
                    "speaker_name": None,
                    "confidence": 0.0,
                    "reason": "没有注册的说话人"
                }
            
            # 计算相似度
            best_match = None
            best_similarity = -1.0
            
            for speaker_id, speaker_data in all_speaker_features.items():
                speaker_name = speaker_data['name']
                features_list = speaker_data['features']
                
                # 计算与该说话人所有样本的平均相似度
                similarities = []
                for registered_features in features_list:
                    similarity = self._calculate_similarity(query_features, registered_features)
                    similarities.append(similarity)
                
                if similarities:
                    avg_similarity = np.mean(similarities)
                    if avg_similarity > best_similarity:
                        best_similarity = avg_similarity
                        best_match = {
                            "speaker_id": speaker_id,
                            "speaker_name": speaker_name,
                            "confidence": avg_similarity
                        }
            
            # 判断是否超过阈值
            if best_match and best_similarity >= threshold:
                return {
                    "identified": True,
                    "speaker_id": best_match["speaker_id"],
                    "speaker_name": best_match["speaker_name"],
                    "confidence": best_similarity,
                    "reason": f"匹配成功，相似度: {best_similarity:.3f}"
                }
            else:
                return {
                    "identified": False,
                    "speaker_id": None,
                    "speaker_name": None,
                    "confidence": best_similarity if best_match else 0.0,
                    "reason": f"相似度不足，最高: {best_similarity:.3f} < {threshold:.3f}"
                }
                
        except Exception as e:
            print(f"❌ 说话人识别失败: {e}")
            return {
                "identified": False,
                "speaker_id": None,
                "speaker_name": None,
                "confidence": 0.0,
                "reason": f"识别过程出错: {str(e)}"
            }
    
    def _calculate_similarity(self, features1: np.ndarray, features2: np.ndarray) -> float:
        """计算特征向量相似度"""
        try:
            # 使用余弦相似度
            dot_product = np.dot(features1, features2)
            norm1 = np.linalg.norm(features1)
            norm2 = np.linalg.norm(features2)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            similarity = dot_product / (norm1 * norm2)
            
            # 将相似度从[-1, 1]映射到[0, 1]
            return (similarity + 1) / 2
            
        except Exception as e:
            print(f"⚠️ 相似度计算失败: {e}")
            return 0.0
    
    def get_speaker_info(self, speaker_id: str) -> Optional[Dict]:
        """获取说话人详细信息"""
        speaker = self.db.get_speaker(speaker_id)
        if not speaker:
            return None
        
        # 获取音频样本
        samples = self.db.get_audio_samples(speaker_id)
        
        # 获取特征向量
        features = self.db.get_speaker_features(speaker_id)
        
        return {
            **speaker,
            'samples': samples,
            'feature_count': len(features),
            'total_duration': sum(sample['duration'] for sample in samples),
            'avg_quality_score': np.mean([s['quality_score'] for s in samples if s['quality_score']]) if samples else 0
        }
    
    def list_speakers(self) -> List[Dict]:
        """列出所有说话人"""
        return self.db.list_speakers()
    
    def delete_speaker(self, speaker_id: str) -> bool:
        """删除说话人"""
        return self.db.delete_speaker(speaker_id)
    
    def delete_audio_sample(self, sample_id: str) -> bool:
        """删除音频样本"""
        return self.db.delete_audio_sample(sample_id)
    
    def get_statistics(self) -> Dict:
        """获取统计信息"""
        return self.db.get_database_stats()
