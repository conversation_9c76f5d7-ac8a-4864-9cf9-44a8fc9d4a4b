"""
人员音频录入数据库模型
管理人员信息、音频样本和特征向量的存储
"""
import sqlite3
import json
import numpy as np
import os
import uuid
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Optional, Tuple
from config import SPEAKER_CONFIG


class SpeakerDatabase:
    """人员音频数据库管理类"""
    
    def __init__(self, db_path: str = None):
        self.db_path = db_path or SPEAKER_CONFIG["database_path"]
        self.audio_samples_dir = Path(SPEAKER_CONFIG["audio_samples_dir"])
        
        # 确保目录存在
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        os.makedirs(self.audio_samples_dir, exist_ok=True)
        
        # 初始化数据库
        self._init_database()
    
    def _init_database(self):
        """初始化数据库表结构"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 人员信息表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS speakers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    speaker_id TEXT UNIQUE NOT NULL,
                    name TEXT NOT NULL,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1,
                    sample_count INTEGER DEFAULT 0
                )
            ''')
            
            # 音频样本表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS audio_samples (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    sample_id TEXT UNIQUE NOT NULL,
                    speaker_id TEXT NOT NULL,
                    file_path TEXT NOT NULL,
                    duration REAL NOT NULL,
                    sample_rate INTEGER NOT NULL,
                    quality_score REAL,
                    voice_activity_ratio REAL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    metadata TEXT,
                    FOREIGN KEY (speaker_id) REFERENCES speakers (speaker_id)
                )
            ''')
            
            # 特征向量表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS speaker_features (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    feature_id TEXT UNIQUE NOT NULL,
                    speaker_id TEXT NOT NULL,
                    sample_id TEXT NOT NULL,
                    feature_vector BLOB NOT NULL,
                    feature_dim INTEGER NOT NULL,
                    extraction_method TEXT DEFAULT 'eres2net_large',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (speaker_id) REFERENCES speakers (speaker_id),
                    FOREIGN KEY (sample_id) REFERENCES audio_samples (sample_id)
                )
            ''')
            
            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_speakers_speaker_id ON speakers (speaker_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_samples_speaker_id ON audio_samples (speaker_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_features_speaker_id ON speaker_features (speaker_id)')
            
            conn.commit()
            print("✅ 人员音频数据库初始化完成")
    
    def add_speaker(self, name: str, description: str = "") -> str:
        """添加新的说话人"""
        speaker_id = f"speaker_{uuid.uuid4().hex[:8]}"
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            try:
                cursor.execute('''
                    INSERT INTO speakers (speaker_id, name, description)
                    VALUES (?, ?, ?)
                ''', (speaker_id, name, description))
                conn.commit()
                print(f"✅ 添加说话人成功: {name} (ID: {speaker_id})")
                return speaker_id
            except sqlite3.IntegrityError:
                print(f"❌ 说话人已存在: {name}")
                raise ValueError(f"说话人已存在: {name}")
    
    def get_speaker(self, speaker_id: str) -> Optional[Dict]:
        """获取说话人信息"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT speaker_id, name, description, created_at, updated_at, 
                       is_active, sample_count
                FROM speakers WHERE speaker_id = ? AND is_active = 1
            ''', (speaker_id,))
            
            row = cursor.fetchone()
            if row:
                return {
                    'speaker_id': row[0],
                    'name': row[1],
                    'description': row[2],
                    'created_at': row[3],
                    'updated_at': row[4],
                    'is_active': bool(row[5]),
                    'sample_count': row[6]
                }
            return None
    
    def list_speakers(self, active_only: bool = True) -> List[Dict]:
        """列出所有说话人"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            query = '''
                SELECT speaker_id, name, description, created_at, updated_at, 
                       is_active, sample_count
                FROM speakers
            '''
            params = []
            
            if active_only:
                query += ' WHERE is_active = 1'
            
            query += ' ORDER BY created_at DESC'
            
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            return [{
                'speaker_id': row[0],
                'name': row[1],
                'description': row[2],
                'created_at': row[3],
                'updated_at': row[4],
                'is_active': bool(row[5]),
                'sample_count': row[6]
            } for row in rows]
    
    def update_speaker(self, speaker_id: str, name: str = None, description: str = None) -> bool:
        """更新说话人信息"""
        updates = []
        params = []
        
        if name is not None:
            updates.append("name = ?")
            params.append(name)
        
        if description is not None:
            updates.append("description = ?")
            params.append(description)
        
        if not updates:
            return False
        
        updates.append("updated_at = CURRENT_TIMESTAMP")
        params.append(speaker_id)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            query = f"UPDATE speakers SET {', '.join(updates)} WHERE speaker_id = ?"
            cursor.execute(query, params)
            conn.commit()
            
            return cursor.rowcount > 0
    
    def delete_speaker(self, speaker_id: str, hard_delete: bool = False) -> bool:
        """删除说话人（软删除或硬删除）"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            if hard_delete:
                # 硬删除：删除所有相关数据
                cursor.execute('DELETE FROM speaker_features WHERE speaker_id = ?', (speaker_id,))
                cursor.execute('DELETE FROM audio_samples WHERE speaker_id = ?', (speaker_id,))
                cursor.execute('DELETE FROM speakers WHERE speaker_id = ?', (speaker_id,))
            else:
                # 软删除：标记为非活跃
                cursor.execute('''
                    UPDATE speakers SET is_active = 0, updated_at = CURRENT_TIMESTAMP 
                    WHERE speaker_id = ?
                ''', (speaker_id,))
            
            conn.commit()
            return cursor.rowcount > 0
    
    def add_audio_sample(self, speaker_id: str, file_path: str, duration: float, 
                        sample_rate: int, quality_score: float = None, 
                        voice_activity_ratio: float = None, metadata: Dict = None) -> str:
        """添加音频样本"""
        sample_id = f"sample_{uuid.uuid4().hex[:8]}"
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 检查说话人是否存在
            cursor.execute('SELECT speaker_id FROM speakers WHERE speaker_id = ? AND is_active = 1', 
                          (speaker_id,))
            if not cursor.fetchone():
                raise ValueError(f"说话人不存在: {speaker_id}")
            
            # 添加样本
            cursor.execute('''
                INSERT INTO audio_samples 
                (sample_id, speaker_id, file_path, duration, sample_rate, 
                 quality_score, voice_activity_ratio, metadata)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (sample_id, speaker_id, file_path, duration, sample_rate,
                  quality_score, voice_activity_ratio, 
                  json.dumps(metadata) if metadata else None))
            
            # 更新说话人样本计数
            cursor.execute('''
                UPDATE speakers SET sample_count = sample_count + 1, 
                       updated_at = CURRENT_TIMESTAMP 
                WHERE speaker_id = ?
            ''', (speaker_id,))
            
            conn.commit()
            print(f"✅ 添加音频样本成功: {sample_id}")
            return sample_id
    
    def get_audio_samples(self, speaker_id: str) -> List[Dict]:
        """获取说话人的所有音频样本"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT sample_id, file_path, duration, sample_rate, quality_score,
                       voice_activity_ratio, created_at, metadata
                FROM audio_samples 
                WHERE speaker_id = ?
                ORDER BY created_at DESC
            ''', (speaker_id,))
            
            rows = cursor.fetchall()
            return [{
                'sample_id': row[0],
                'file_path': row[1],
                'duration': row[2],
                'sample_rate': row[3],
                'quality_score': row[4],
                'voice_activity_ratio': row[5],
                'created_at': row[6],
                'metadata': json.loads(row[7]) if row[7] else None
            } for row in rows]
    
    def add_speaker_feature(self, speaker_id: str, sample_id: str, 
                           feature_vector: np.ndarray, extraction_method: str = "eres2net_large") -> str:
        """添加说话人特征向量"""
        feature_id = f"feature_{uuid.uuid4().hex[:8]}"
        
        # 将numpy数组转换为二进制数据
        feature_blob = feature_vector.astype(np.float32).tobytes()
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO speaker_features 
                (feature_id, speaker_id, sample_id, feature_vector, feature_dim, extraction_method)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (feature_id, speaker_id, sample_id, feature_blob, 
                  len(feature_vector), extraction_method))
            
            conn.commit()
            print(f"✅ 添加特征向量成功: {feature_id}")
            return feature_id
    
    def get_speaker_features(self, speaker_id: str) -> List[Tuple[str, np.ndarray]]:
        """获取说话人的所有特征向量"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT feature_id, feature_vector, feature_dim
                FROM speaker_features 
                WHERE speaker_id = ?
                ORDER BY created_at DESC
            ''', (speaker_id,))
            
            results = []
            for row in cursor.fetchall():
                feature_id, feature_blob, feature_dim = row
                # 将二进制数据转换回numpy数组
                feature_vector = np.frombuffer(feature_blob, dtype=np.float32)
                results.append((feature_id, feature_vector))
            
            return results
    
    def get_all_speaker_features(self) -> Dict[str, List[np.ndarray]]:
        """获取所有活跃说话人的特征向量"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT sf.speaker_id, s.name, sf.feature_vector, sf.feature_dim
                FROM speaker_features sf
                JOIN speakers s ON sf.speaker_id = s.speaker_id
                WHERE s.is_active = 1
                ORDER BY sf.created_at DESC
            ''')
            
            speaker_features = {}
            for row in cursor.fetchall():
                speaker_id, name, feature_blob, feature_dim = row
                feature_vector = np.frombuffer(feature_blob, dtype=np.float32)
                
                if speaker_id not in speaker_features:
                    speaker_features[speaker_id] = {
                        'name': name,
                        'features': []
                    }
                
                speaker_features[speaker_id]['features'].append(feature_vector)
            
            return speaker_features
    
    def delete_audio_sample(self, sample_id: str) -> bool:
        """删除音频样本"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 获取样本信息
            cursor.execute('SELECT speaker_id, file_path FROM audio_samples WHERE sample_id = ?', 
                          (sample_id,))
            row = cursor.fetchone()
            if not row:
                return False
            
            speaker_id, file_path = row
            
            # 删除相关特征向量
            cursor.execute('DELETE FROM speaker_features WHERE sample_id = ?', (sample_id,))
            
            # 删除样本记录
            cursor.execute('DELETE FROM audio_samples WHERE sample_id = ?', (sample_id,))
            
            # 更新说话人样本计数
            cursor.execute('''
                UPDATE speakers SET sample_count = sample_count - 1,
                       updated_at = CURRENT_TIMESTAMP 
                WHERE speaker_id = ?
            ''', (speaker_id,))
            
            # 删除音频文件
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    print(f"✅ 删除音频文件: {file_path}")
            except Exception as e:
                print(f"⚠️ 删除音频文件失败: {e}")
            
            conn.commit()
            return True
    
    def get_database_stats(self) -> Dict:
        """获取数据库统计信息"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 统计说话人数量
            cursor.execute('SELECT COUNT(*) FROM speakers WHERE is_active = 1')
            active_speakers = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM speakers')
            total_speakers = cursor.fetchone()[0]
            
            # 统计样本数量
            cursor.execute('SELECT COUNT(*) FROM audio_samples')
            total_samples = cursor.fetchone()[0]
            
            # 统计特征向量数量
            cursor.execute('SELECT COUNT(*) FROM speaker_features')
            total_features = cursor.fetchone()[0]
            
            # 计算总音频时长
            cursor.execute('SELECT SUM(duration) FROM audio_samples')
            total_duration = cursor.fetchone()[0] or 0
            
            return {
                'active_speakers': active_speakers,
                'total_speakers': total_speakers,
                'total_samples': total_samples,
                'total_features': total_features,
                'total_duration_seconds': total_duration,
                'total_duration_minutes': total_duration / 60,
                'database_path': self.db_path,
                'audio_samples_dir': str(self.audio_samples_dir)
            }
