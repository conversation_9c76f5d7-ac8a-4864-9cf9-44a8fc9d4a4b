"""
音频异步处理模块 - 专门处理实时音频转录的异步任务
"""
import threading
import time
import numpy as np
from queue import Queue, Empty
from flask_socketio import emit


class AudioProcessingManager:
    """音频处理管理器 - 负责异步处理音频转录任务"""

    def __init__(self, socketio, realtime_service, config):
        self.socketio = socketio
        self.realtime_service = realtime_service
        self.config = config

        # 处理队列和线程
        self.processing_queue = Queue(maxsize=10)  # 限制队列大小
        self.processing_thread = None
        self.is_processing = False

        # 统计信息
        self.processed_count = 0
        self.total_process_time = 0

    def start_processing(self):
        """启动音频处理线程"""
        if not self.is_processing:
            self.is_processing = True
            self.processing_thread = threading.Thread(
                target=self._audio_processing_worker,
                daemon=True,
                name="AudioProcessor"
            )
            self.processing_thread.start()
            print("🚀 音频异步处理线程已启动")
            return True
        return False

    def stop_processing(self):
        """停止音频处理线程"""
        if self.is_processing:
            self.is_processing = False
            if self.processing_thread and self.processing_thread.is_alive():
                self.processing_thread.join(timeout=5)
            print("⏹️ 音频异步处理线程已停止")
            self._print_statistics()

    def add_audio_task(self, session_id, buffer_array, session_config):
        """添加音频处理任务到队列 - 优化版本"""
        try:
            if self.processing_queue.qsize() >= 5:  # 降低队列大小，避免积压
                try:
                    old_task = self.processing_queue.get_nowait()
                    print(f"⚠️ 队列已满，丢弃旧任务(长度: {len(old_task['buffer_array'])})")
                except Empty:
                    pass

            # 确保音频长度足够
            min_samples = int(session_config['sample_rate'] * 2.0)  # 至少2秒
            if len(buffer_array) < min_samples:
                print(f"⚠️ 音频太短({len(buffer_array)}/{min_samples})，跳过处理")
                return False

            task = {
                'session_id': session_id,
                'buffer_array': buffer_array.copy(),
                'session_config': session_config,
                'timestamp': time.time()
            }

            self.processing_queue.put(task, timeout=1)
            print(f"📝 音频任务已加入队列(长度: {len(buffer_array)}样本)，队列大小: {self.processing_queue.qsize()}")
            return True

        except Exception as e:
            print(f"❌ 添加音频任务失败: {e}")
            return False

    def _audio_processing_worker(self):
        """音频处理工作线程 - 优化版本"""
        print("🔧 音频处理工作线程开始运行")

        while self.is_processing:
            try:
                # 从队列获取任务
                task = self.processing_queue.get(timeout=1)

                session_id = task['session_id']
                buffer_array = task['buffer_array']
                session_config = task['session_config']
                task_age = time.time() - task['timestamp']

                # 检查任务是否过期
                if task_age > 20:  # 20秒过期
                    print(f"⚠️ 任务过期({task_age:.1f}s)，跳过处理")
                    self.processing_queue.task_done()
                    continue

                # 音频预处理 - 确保完整性
                audio_duration = len(buffer_array) / session_config['sample_rate']
                print(f"🔄 开始处理音频 - 时长: {audio_duration:.1f}s, 任务年龄: {task_age:.1f}s")

                start_time = time.time()

                # 执行音频转录
                try:
                    if session_config['enable_speaker']:
                        # 检查是否有选择特定的说话人
                        selected_speakers = session_config.get('selected_speakers', [])
                        if selected_speakers:
                            # 使用定向说话人识别
                            result = self.realtime_service.transcribe_audio_with_targeted_speaker_identification(
                                buffer_array, session_config['sample_rate'], selected_speakers
                            )
                        else:
                            # 使用通用说话人分离
                            result = self.realtime_service.transcribe_audio_with_speaker(
                                buffer_array, session_config['sample_rate']
                            )

                        if result.get('text', '').strip():
                            segmented_text = []
                            for segment in result.get("segments", []):
                                segmented_text.append({
                                    "start_time": segment.get("start", 0),
                                    "end_time": segment.get("end", 0),
                                    "speaker": segment.get("speaker", "Speaker_0"),
                                    "text": segment.get("text", "")
                                })

                            self._send_result(session_id, result.get("text", ""), segmented_text, start_time,
                                              audio_duration)
                        else:
                            print("⚠️ 转录结果为空")
                    else:
                        result = self.realtime_service.transcribe_audio_simple(
                            buffer_array, session_config['sample_rate']
                        )

                        if result.get('text', '').strip():
                            self._send_result(session_id, result.get("text", ""), None, start_time, audio_duration)
                        else:
                            print("⚠️ 转录结果为空")

                except Exception as e:
                    print(f"❌ 音频转录失败: {e}")
                    import traceback
                    traceback.print_exc()
                    self._send_error(session_id, str(e))

                self.processing_queue.task_done()

            except Empty:
                # 队列为空，继续等待
                continue
            except Exception as e:
                print(f"❌ 处理线程异常: {e}")
                continue

        print("🔧 音频处理工作线程已退出")

    def _send_result(self, session_id, text, segmented_text, start_time, audio_duration):
        """发送转录结果 - 增加音频时长信息"""
        process_time = time.time() - start_time
        self.processed_count += 1
        self.total_process_time += process_time

        result_data = {
            "voice_text": text,
            "process_time": f"{process_time:.2f}s",
            "audio_duration": f"{audio_duration:.1f}s"
        }

        if segmented_text:
            result_data["segmented_text"] = segmented_text

        self.socketio.emit('realtime_result', {
            "code": 200,
            "message": "实时转录结果",
            "data": result_data
        }, room=session_id)

        print(
            f"✅ 结果已发送: {session_id}, 文本长度: {len(text)}, 处理耗时: {process_time:.2f}s, 音频时长: {audio_duration:.1f}s")

    def _send_error(self, session_id, error_message):
        """发送错误信息"""
        self.socketio.emit('realtime_error', {
            "code": 500,
            "message": f"转录失败: {error_message}",
            "data": None
        }, room=session_id)

    def _print_statistics(self):
        """打印处理统计信息"""
        if self.processed_count > 0:
            avg_time = self.total_process_time / self.processed_count
            print(f"📊 处理统计: 总任务数={self.processed_count}, 平均耗时={avg_time:.2f}s")

    def get_queue_status(self):
        """获取队列状态"""
        return {
            "queue_size": self.processing_queue.qsize(),
            "is_processing": self.is_processing,
            "processed_count": self.processed_count,
            "avg_process_time": self.total_process_time / max(1, self.processed_count)
        }